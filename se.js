var SE = {
    Geocode: function (uri) {
        this.uri = uri;
    },
    Data: function (uri) {
        this.uri = uri;
    },
    CTS: function (uri) {
        this.uri = uri;
    },
    C3D: function (uri) {
        this.uri = uri;
    },
    WNS: function (uri) {
        this.uri = uri;
    },
    WCS: function (uri, name) {
        this.uri = uri;
        this.name = name;
    }
}
SE.Geocode.prototype.findAddressCandidates = function (option) {
    var arr = ['singleLine', 'maxLocations', 'outFields', 'location', 'distance', 'category'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/findAddressCandidates';
    // alert(url)
    console.log(data)
        var option1 = {
        url: url,
        data: data,
        dataType: 'json',
        success: function (data) {
            var res = {};
            if (typeof option.successCallback == 'function') {
                if (data.error&&data.error!=0) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
    // gisserverLibUtil.postFile(option1)
}
SE.Geocode.prototype.geocodeAddresses = function (option) {
    var arr = ['addresses', 'category'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/geocodeAddresses';
    var option1 = {
        url: url,
        data: data,
        dataType: 'text',
        success: function (data) {
            var res = {};
            if (typeof option.successCallback == 'function') {
                if (data.error&&data.error!=0) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}
SE.Geocode.prototype.reverseGeocode = function (option) {
    var arr = ['location', 'distance', 'category'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/reverseGeocode';
    var option1 = {
        url: url,
        data: data,
        dataType: 'text',
        success: function (data) {
            var res = {};
            if (typeof option.successCallback == 'function') {
                if (data.error&&data.error!=0) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}

/**************   Data   *************/
SE.Data.prototype.shapefileImport = function (option) {
    var arr = ['url', 'driver', 'username', 'password', 'buildSpatialDB', 'name', 'shapefile', 'dbasefile', 'charset', 'srs', 'action'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var option1 = {
        url: this.uri,
        data: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.postFile(option1);
}
SE.Data.prototype.excelImport = function (option) {
    var arr = ['url', 'driver', 'username', 'password', 'buildSpatialDB', 'name', 'excelfile', 'geoFields', 'geoType', 'srs', 'action'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var option1 = {
        url: this.uri,
        data: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.postFile(option1);
}
/****************    wns   ************/
SE.WNS.prototype.findConnected = function (option) {
    var data = gisserverLibUtil.judgeOption(option, ['flag', 'tolerance', 'barriers', 'featureType', 'propertyName', 'traceEnd', 'returnfalg', 'format'])
    data.request = "FindConnected";
    var option1 = {
        url: this.uri,
        params: data,
        dataType: "text",
        success: function (data) {
            var res = {};
            if (typeof option.successCallback == 'function') {
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
SE.WNS.prototype.findDisconnected = function (option) {
    var data = gisserverLibUtil.judgeOption(option, ['flag', 'tolerance', 'barriers', 'featureType', 'propertyName', 'traceEnd', 'returnfalg', 'format'])
    data.request = "FindDisconnected";
    var option1 = {
        url: this.uri,
        params: data,
        dataType: "text",
        success: function (data) {
            var res = {};
            if (typeof option.successCallback == 'function') {
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
SE.WNS.prototype.findPath = function (option) {
    var data = gisserverLibUtil.judgeOption(option, ['start', 'end', 'tolerance', 'featureType', 'propertyName', 'returnfalg', 'format'])
    data.request = "FindPath";
    var option1 = {
        url: this.uri,
        params: data,
        dataType: "text",
        success: function (data) {
            var res = {};
            if (typeof option.successCallback == 'function') {
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
SE.WNS.prototype.findRoute = function (option) {
    var data = gisserverLibUtil.judgeOption(option, ['start', 'end', 'tolerance', 'propertyName', 'tactic', 'format'])
    data.request = "FindRoute";
    var option1 = {
        url: this.uri,
        params: data,
        dataType: "text",
        success: function (data) {
            var res = {};
            if (typeof option.successCallback == 'function') {
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
SE.WNS.prototype.findServicearea = function (option) {
    var data = gisserverLibUtil.judgeOption(option, ['flag', 'breaks', 'tolerance', 'tactic', 'returnTraces', 'tolerance', 'propertyName', 'format'])
    data.request = "FindServiceArea";
    var option1 = {
        url: this.uri,
        params: data,
        dataType: "text",
        success: function (data) {
            var res = {};
            if (typeof option.successCallback == 'function') {
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
SE.WNS.prototype.findEdge = function (option) {
    var data = gisserverLibUtil.judgeOption(option, ['flag', 'tolerance', 'featureType', 'propertyName', 'format'])
    data.request = "FindEdge";
    var option1 = {
        url: this.uri,
        params: data,
        dataType: "text",
        success: function (data) {
            var res = {};
            if (typeof option.successCallback == 'function') {
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
/******************  cts  ***************/
SE.CTS.prototype.ctsService = function (option) {
    var option1 = {
        url: this.uri,
        async: false,
        dataType: 'text',
        params: {},
        success: function (res) {
            var result = {};
            if (typeof option.successCallback == 'function') {
                if(res.error){
                    result.error = error;
                    result.message = res.message;
                    option.successCallback(result);
                }else{
                    result.error = 0;
                    result.data = res;
                    option.successCallback(result);
                }
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/******************  c3d  ***************/
SE.C3D.prototype.c3dService = function (option) {
    var option1 = {
        url: this.uri + '/tileset.json',
        async: false,
        dataType: 'json',
        params: {},
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
/*******************************************/
SE.WCS.prototype.serviceList = function (option) {
    var url = this.uri + '/wcsserver/' + this.name;
    gisserverLibUtil.get({
        url: url,
        params: {
            VERSION: "1.3.0",
            SERVICE: 'WMS',
            REQUEST: "GetCapabilities",
        },
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                var res = {};
                if(data.error){
                    res.error = data.error;
                    res.message = data.message;
                }else{
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    });
}
SE.WCS.prototype.imageServer = function (option) {
    var url = this.uri + '/rest/services/' + this.name + '/' + option.layerDni + '/ImageServer/';
    var option1 = {
        async: false,
        url: url,
        data: {},
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}
SE.WCS.prototype.compute = function (option) {
    var arr = ['expr', 'args', 'bbox', 'size', 'imageSR', 'bboxSR', 'time', 'format', 'bandIds', 'noData', 'f'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/rest/services/' + this.name + '/ComputeServer';
    var option1 = {
        url: url,
        data: data,
        dataType: option.dataType,
        success: function (data) {
            if (option.f == 'image') {
                if (data.type) {
                    if (data.type == "image/jpeg" || data.type == "image/png" || data.type == "image/gif") {
                        if (typeof option.successCallback == 'function') {
                            src = window.URL.createObjectURL(data);
                            var jsonObj = {};
                            jsonObj.error = 0;
                            jsonObj.data = src;
                            option.successCallback(jsonObj);
                        }
                    } else {
                        var reader = new FileReader();
                        reader.readAsText(data, 'utf-8');
                        reader.onload = function (e) {
                            console.log("-----------------blobString----------------------------------------")
                            console.log(reader.result);
                            console.log("-----------------blobString----------------------------------------")
                            var jsonObj = {};
                            jsonObj.error = 10000;
                            if (reader.result) {
                                jsonObj.message = reader.result;
                            } else {
                                jsonObj.message = 'Not Found'
                            }
                            option.successCallback(jsonObj)
                        }
                    }
                } else {
                    var jsonObj = {};
                    jsonObj.error = 10000;
                    jsonObj.message = data;
                    option.successCallback(jsonObj);
                }
            } else {
                var jsonObj = {};
                jsonObj.error = 10000;
                jsonObj.data = data;
                option.successCallback(jsonObj);
            }

        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}
SE.WCS.prototype.exportMap = function (option) {
    var arr = ['bbox', 'size', 'imageSR', 'bboxSR', 'time', 'format', 'bandIds', 'renderingRule', 'noData', 'f'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/rest/services/' + this.name + '/' + option.layerDni + '/ImageServer/exportImage';
    var option1 = {
        url: url,
        data: data,
        async: false,
        dataType: 'blob',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}
SE.WCS.prototype.identify = function (option) {
    var arr = ['geometry', 'geometryType', 'time'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/rest/services/' + this.name + '/' + option.layerDni + '/ImageServer/identify';
    var option1 = {
        url: url,
        data: data,
        async: false,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}
SE.WCS.prototype.tile = function (option) {
    var arr = ['level', 'row', 'column'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var option1 = {
        url: this.uri + '/rest/services/' + this.name + '/' + option.layerDni + '/ImageServer/tile/' + option.level + '/' + option.row + '/' + option.column,
        params: data,
        async: false,
        dataType: 'blob',
        success: function (data) {
            if (data.type) {
                if (data.type == "image/jpeg" || data.type == "image/png" || data.type == "image/gif") {
                    if (typeof option.successCallback == 'function') {
                        src = window.URL.createObjectURL(data);
                        var jsonObj = {};
                        jsonObj.error = 0;
                        jsonObj.data = src;
                        option.successCallback(jsonObj);
                    }
                } else {
                    var reader = new FileReader();
                    reader.readAsText(data, 'utf-8');
                    reader.onload = function (e) {
                        var jsonObj = {};
                        jsonObj.error = 10000;
                        if (reader.result) {
                            jsonObj.message = reader.result;
                        } else {
                            jsonObj.message = 'Not Found'
                        }
                        option.successCallback(jsonObj)
                    }
                }
            } else {
                var jsonObj = {};
                jsonObj.error = 10000;
                jsonObj.message = data;
                option.successCallback(jsonObj);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
SE.WCS.prototype.keyProperties = function (option) {
    var url = this.uri + '/rest/services/' + this.name + '/' + option.layerDni + '/ImageServer/keyProperties';
    var option1 = {
        async: false,
        url: url,
        params: {},
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
SE.WCS.prototype.getMap = function(option){
    var data = gisserverLibUtil.judgeOption(option, [ 'layers', 'crs', 'bbox', 'width', 'height', 'format', 'transparent', 'imageSR', 'bandids', 'time', 'renderingrule', 'nodate'])
    data.version = "1.3.0";
    data.request = "GetMap";
    var option1 = {
        url: this.uri+"/wcsserver/" + this.name,
        dataType: 'blob',
        params: data,

        success: function (data) {
            if (data.type == "image/jpeg" || data.type == "image/png" || data.type == "image/gif") {
                if (typeof option.successCallback == 'function') {
                    src = window.URL.createObjectURL(data);
                    var jsonObj = {};
                    jsonObj.error = 0;
                    jsonObj.data = src;
                    option.successCallback(jsonObj);

                }
            } else {
                var reader = new FileReader();
                reader.readAsText(data, 'utf-8');
                reader.onload = function (e) {
                    console.log("-----------------blobString----------------------------------------")
                    console.log(reader.result);
                    console.log("-----------------blobString----------------------------------------")
                    if (reader.result.indexOf("ServiceException") > -1) {
                        var xmlObj = string2Xml(reader.result);
                        var str = "";
                        var xmlNodesServiceException = xmlObj.querySelectorAll("ServiceException");
                        for (var i = 0; i < xmlNodesServiceException.length; i++) {

                            str += xmlNodesServiceException[i].innerHTML;
                        }
                        var jsonObj = {};
                        console.log(data)
                        jsonObj.error = 0;
                        jsonObj.message = str;
                        option.successCallback(jsonObj)
                    } else {
                        var jsonObj = {};
                        jsonObj.error = 10000;
                        jsonObj.message = reader.result;
                        option.successCallback(jsonObj)
                    }

                }
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

