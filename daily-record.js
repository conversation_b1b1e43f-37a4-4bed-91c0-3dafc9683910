var dailyRecord = {
    url: '',
    setServerUrl: function(url){
        this.url = url;
    },
    getLogList: function (start, end, successCallback, errorCallback) {
        var option = {
            url: this.url + '/api/logs',
            params: {
				start: start,
				end: end
			},
            dataType: 'json',
            success: function (data){
                if(typeof successCallback == 'function'){
                    successCallback(data);
                }
            },
            error: function (){
                if(typeof errorCallback == 'function'){
                    errorCallback(data);
                }
            }
        }
        gisserverLibUtil.get(option);
    },
    getLog: function (name, successCallback, errorCallback) {
        var option = {
            url: this.url + '/api/logs/content',
            params: {
                name: name
            },
            dataType: 'json',
            success: function (data){

                if(typeof successCallback == 'function'){
                    successCallback(data);
                }
            },
            error: function (){
                if(typeof errorCallback == 'function'){
                    errorCallback(data);
                }
            }
        }
        gisserverLibUtil.get(option);
    },
    logIndex: 0,
    downLogs: function(arr){
        if(arr && arr.length>0){
            this.logIndex = 0;
            this.downlog(arr);
        }
    },
    downlog: function(arr){
        if(this.logIndex < arr.length){
            var service = arr[this.logIndex];
            var name = service.name;
            this.downLoadLog(name);
            this.logIndex++;
            var that = this;
            setTimeout(function(){
                that.downlog(arr);
            }, 1000)
        }
    },
    downLoadLog: function (name) {
		var url = this.url + '/api/logs/download?name=' + name;
		 // window.open(url);
        public_download_file(url, name);
    },
	deleteLog: function(name, successCallback, errorCallback){
		// var option = {
		//     url: this.url + '/api/logs',
		//     params: {
		//         name: name
		//     },
		//     dataType: 'json',
		//     success: function (data){
		//         if(typeof successCallback == 'function'){
		//             successCallback(data);
		//         }
		//     },
		//     error: function (){
		//         if(typeof errorCallback == 'function'){
		//             errorCallback(data);
		//         }
		//     }
		// }
		// gisserverLibUtil.delete(option);
	}
}
