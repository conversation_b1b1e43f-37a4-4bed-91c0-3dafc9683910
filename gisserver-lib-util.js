var gisserverLibUtil = {
	/**
	 * @des 通用接口判断option参数 此方法用于锁定传入参数，防止非法请求（不期望的请求）
	 * @option 输入参数对象
	 * @arr 要判断的参数
	 * @return 返回处理完毕的参数对象
	 * */
	judgeOption: function(option, arr) {
		var obj = {}
		for (var i in arr) {
			if (typeof option[arr[i]] == 'function') {
				//option[arr[i]] = '';
			} else if (typeof option[arr[i]] != 'undefined' &&
				option[arr[i]] != 'NaN') {
				obj[arr[i]] = option[arr[i]];
			} else {

			}
		}
		//不传入arr参数 或者  arr参数长度为0  直接处理option
		if (!arr || arr.length == 0) {
			for (var i in option) {
				if (typeof option[i] == 'function') {
					//option[arr[i]] = '';
				} else {
					obj[i] = option[i];
				}
			}
		}
		return obj;
	},
	/**
	 * @des 通用方法 get请求
	 * @option 输入参数
	 * */
	get: function(option) {
		var xhr = new XMLHttpRequest(); //this.createXHR()
		var str = "?"
		for (var i in option.params) {
			var tempI = this.URLencode(i);
			var tempData = this.URLencode(option.params[i]);
			str += tempI + "=" + tempData + "&";
		}
		if (str.length > 0) {
			str = str.substr(0, str.length - 1);
		}
		if (option.async) {
			xhr.open('GET', option.url + str, true);
		} else {
			xhr.open('GET', option.url + str, false);
		}
		if(typeof option.success == "function"){
			var successFuc = option.success;
			option.success = function(data){
				if(typeof GisServer == 'function' && typeof GisServer.prototype.jumpLogin == "function"){
					if((option.url + str).indexOf("gisserver/console?logout=1")>-1){
						successFuc(data);
					} else {
						GisServer.prototype.jumpLogin(data, successFuc);
					}
				} else {
					successFuc(data);
				}
			}
		}
		xhr.open('GET', option.url + str, true);
		if (option.dataType == "html" || option.dataType == "xml") {
			xhr.responseType = "document";
		} else {
			xhr.responseType = option.dataType;
		}
		if (typeof(option.success) != 'function') {
			alert('option.success 不是一个方法')
		} else if (typeof(option.error) != 'function') {
			alert('option.error 不是一个方法')
		} else {
			xhr.onload = function() {
				//判断登录是否过期，是否报错
				// console.log(xhr)
				try {
					if (xhr.statusText == 'Unauthorized') {
						var result = {
							error: 401,
							message: '用户未登录！'
						};
						option.success(result);
						return false;
					} else {
						try {
							var data = xhr.responseText;
							if (data.indexOf(
									'<th colspan="2"><span style="font-size: 24px">管理员登录</span></th>') > -
								1) {
								option.success({
									error: 401,
									message: '用户未登录！'
								});
								return false;
							} else if (data.indexOf('Error report</title>') > -1) {
								option.success({
									error: 500,
									message: xhr.responseText
								});
								return false;
							}
						} catch (e) {
							var data = xhr.response;
							if (typeof data == 'String') {
								if (data.indexOf(
										'<th colspan="2"><span style="font-size: 24px">管理员登录</span></th>') >
									-1) {
									option.success({
										error: 401,
										message: '用户未登录！'
									});
									return false;
								} else if (data.indexOf('Error report</title>') > -1) {
									option.success({
										error: 500,
										message: xhr.responseText
									});
									return false;
								}
							}
						}

					}
				} catch (e) {
					console.log(e)
				}
				if (option.dataType == 'json') {

					if (xhr.responseJSON) {

						option.success(xhr.responseJSON)
					} else {
						try {
							var data = JSON.parse(xhr.responseText);
							console.log('---------------------------------------------')
							console.log(data)
							option.success(data);
						} catch (e) {
							try {
								var data;
								if (typeof xhr.response == 'object') {
									data = xhr.response;
								} else {
									data = JSON.parse(xhr.response);
								}
								option.success(data);
							} catch (e) {
								try {
									option.success({
										error: 500,
										message: xhr.responseText
									});
								} catch (e) {
									try {
										option.success({
											error: 500,
											message: xhr.response
										});
									} catch (e) {
										option.success({
											error: 500,
											message: '不支持的返回类型！'
										});
									}
								}
							}
						}
					}
				} else if (option.dataType == 'text') {
					if (xhr.responseText == undefined) {

						option.success({
							error: 500,
							message: '不支持的返回类型！'
						})
					} else {
						// {error: 0, data: xhr.responseText,message:''}
						option.success(xhr.responseText)
					}
				} else if (option.dataType == 'xml') {
					if (xhr.responseXML == undefined) {
						option.success({
							error: 500,
							message: '不支持的返回类型！'
						})
					} else {
						// {error: 0, data: xhr.responseXML,message:''}
						option.success(xhr.responseXML)
					}
				} else if (option.dataType == 'html') {
					if (xhr.responseHTML == undefined) {
						option.success({
							error: 500,
							message: '不支持的返回类型！'
						})
					} else {
						// {error: 0, data: xhr.responseHTML,message:''}
						option.success(xhr.responseHTML)
					}
				} else if (option.dataType == 'blob') {
					if (xhr.response == undefined) {
						option.success({
							error: 500,
							message: xhr.responseText
						})
					} else {
						option.success(xhr.response);
					}
				}
			}
			xhr.onerror = function(e) {
				console.log('请求失败' + e);
				option.error({
					error: 500,
					message: '请求失败' + e
				});
			}
		}
		xhr.send();
	},
	/**
	 * @des 通用方法 post请求
	 * @option 输入参数
	 * */
	post: function(option) {

		var xhr = this.createXHR()
		if (!option.async) {
			option.async = true;
		}
		option.async = true;
		xhr.open('post', option.url, option.async);
		// if(option.url == '/wfsserver/my_wfs'){
		//     xhr.setRequestHeader('Content-Type', 'text/xml; subtype="gml/3.1.1";charset=UTF-8');

		// }else{
		xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded;charset=UTF-8');
		// xhr.setRequestHeader('X-Requested-With','XMLHttpRequest');
		// }
		var sendStr = '';
		if (typeof option.data == 'object') {
			for (var i in option.data) {
				var tempI = this.URLencode(i);
				var tempData = this.URLencode(option.data[i]);
				// var tempI = i;
				// var tempData = option.data[i];
				sendStr += tempI + '=' + tempData + '&';
			}
			if (sendStr.length > 0) {
				sendStr = sendStr.substr(0, sendStr.length - 1);
			}
		} else {
			sendStr = option.data;
		}
		// sendStr = this.URLencode(sendStr);
		console.log('sendStr', sendStr)

		if(typeof option.success == "function"){
			var successFuc = option.success;
			option.success = function(data){
				if(typeof GisServer == 'function' && typeof GisServer.prototype.jumpLogin == "function"){
					GisServer.prototype.jumpLogin(data, successFuc);
				} else {
					successFuc(data);
				}
			}
		}
		xhr.send(sendStr);
		// var formData = new FormData();
		// for (var key in option.data) {
		//     formData.append(key, option.data[key])
		// }
		//       xhr.send(formData);
		if (typeof(option.success) != 'function') {
			alert('option.success 不是一个方法')
		} else if (typeof(option.error) != 'function') {
			alert('option.error 不是一个方法')
		} else {
			xhr.onload = function() {
				//判断登录是否过期，是否报错
				// console.log("---------------------------xhr-------------------------------")
				// console.log(xhr)
				try {
					if (xhr.statusText == 'Unauthorized') {
						var result = {
							error: 401,
							message: '用户未登录！'
						};
						option.success(result);
						return false;
					} else {
						var data = xhr.responseText;
						if (data.indexOf(
							'<th colspan="2"><span style="font-size: 24px">管理员登录</span></th>') > -1) {
							option.success({
								error: 401,
								message: '用户未登录！'
							});
							return false;
						} else if (data.indexOf('Error report</title>') > -1) {
							option.success({
								error: 500,
								message: xhr.responseText
							});
							return false;
						}
					}
				} catch (e) {
					console.log(e)
				}
				if (option.dataType == 'json') {
					if (xhr.responseJSON) {
						option.success(xhr.responseJSON)
					} else {
						try {
							var data = JSON.parse(xhr.responseText);
							option.success(data);
						} catch (e) {
							try {
								var data;
								if (typeof xhr.response == 'object') {
									data = xhr.response;
								} else {
									data = JSON.parse(xhr.response);
								}
								option.success(data);
							} catch (e) {
								try {
									option.success({
										error: 500,
										message: xhr.responseText
									});
								} catch (e) {
									try {
										option.success({
											error: 500,
											message: xhr.response
										});
									} catch (e) {
										option.success({
											error: 500,
											message: '不支持的返回类型！'
										});
									}
								}
							}
						}
					}
				} else if (option.dataType == 'text') {
					if (xhr.responseText == undefined) {
						option.success({
							error: 500,
							message: '不支持的返回类型！'
						})
					} else {
						option.success(xhr.responseText)
					}
				} else if (option.dataType == 'xml') {
					if (xhr.responseXML == undefined) {
						option.success({
							error: 500,
							message: xhr.responseText
						})
					} else {
						option.success(xhr.responseXML)
					}
				} else if (option.dataType == 'html') {
					if (xhr.responseHTML == undefined) {
						option.success({
							error: 500,
							message: xhr.responseText
						})
					} else {
						option.success(xhr.responseHTML)
					}
				} else if (option.dataType == 'blob') {
					// console.log('-------------xhr---------xxx------')
					// console.log(xhr)
					if (xhr.response == undefined) {
						option.success({
							error: 500,
							message: xhr.responseText
						})
					} else {
						option.success(xhr.response)
					}
				}
			}
			xhr.onerror = function(e) {
				console.log('请求失败' + e);
				option.error({
					error: 500,
					message: '请求失败' + e
				});
			}
		}
	},
	/**
	 * @des 通用方法 post请求  传文件和参数
	 * @option 输入参数
	 * */
	postFile: function(option) {
		var formData = new FormData();
		for (var key in option.data) {
			formData.append(key, option.data[key])
		}
		var xhr = this.createXHR()
		if (!option.async) {
			option.async = true;
		}

		if(typeof option.success == "function"){
			var successFuc = option.success;
			option.success = function(data){
				if(GisServer && typeof GisServer.prototype.jumpLogin == "function"){
					GisServer.prototype.jumpLogin(data, successFuc);
				} else {
					successFuc(data);
				}
			}
		}

		xhr.open('post', option.url, option.async);
		xhr.send(formData);
		if (typeof(option.success) != 'function') {
			alert('option.success 不是一个方法')
		} else if (typeof(option.error) != 'function') {
			alert('option.error 不是一个方法')
		} else {
			xhr.onload = function() {
				//判断登录是否过期，是否报错
				// console.log(xhr)
				try {
					if (xhr.statusText == 'Unauthorized') {
						var result = {
							error: 401,
							message: '用户未登录！'
						};
						option.success(result);
						return false;
					} else {
						var data = xhr.responseText;
						if (data.indexOf(
							'<th colspan="2"><span style="font-size: 24px">管理员登录</span></th>') > -1) {
							option.success({
								error: 401,
								message: '用户未登录！'
							});
							return false;
						} else if (data.indexOf('Error report</title>') > -1) {
							option.success({
								error: 500,
								message: xhr.responseText
							});
							return false;
						}
					}
				} catch (e) {
					console.log(e)
				}
				if (option.dataType == 'json') {
					if (xhr.responseJSON) {
						option.success(xhr.responseJSON)
					} else {
						try {
							var data = JSON.parse(xhr.responseText);
							option.success(data);
						} catch (e) {
							try {
								var data;
								if (typeof xhr.response == 'object') {
									data = xhr.response;
								} else {
									data = JSON.parse(xhr.response);
								}
								option.success(data);
							} catch (e) {
								try {
									option.success({
										error: 500,
										message: xhr.responseText
									});
								} catch (e) {
									try {
										option.success({
											error: 500,
											message: xhr.response
										});
									} catch (e) {
										option.success({
											error: 500,
											message: '不支持的返回类型！'
										});
									}
								}
							}
						}
					}
				} else if (option.dataType == 'text') {
					if (xhr.responseText == undefined) {
						option.success({
							error: 500,
							message: '不支持的返回类型！'
						})
					} else {
						option.success(xhr.responseText)
					}
				} else if (option.dataType == 'xml') {
					if (xhr.responseXML == undefined) {
						option.success({
							error: 500,
							message: xhr.responseText
						})
					} else {
						option.success(xhr.responseXML)
					}
				} else if (option.dataType == 'html') {
					if (xhr.responseHTML == undefined) {
						option.success({
							error: 500,
							message: xhr.responseText
						})
					} else {
						option.success(xhr.responseHTML)
					}
				} else if (option.dataType == 'blob') {
					if (xhr.response == undefined) {
						option.success({
							error: 500,
							message: xhr.responseText
						})
					} else {
						option.success(xhr.response)
					}
				}
			}
			xhr.onerror = function(e) {
				console.log('请求失败' + e);
				option.error({
					error: 500,
					message: '请求失败' + e
				});
			}
		}
	},
	/**
	 * @des delete请求
	 * */
	delete: function(option) {
		var str = "?"
		for (var i in option.params) {
			str += i + "=" + option.params[i] + "&";
		}
		var xhr = this.createXHR();
		xhr.open("DELETE", option.url + str, true);
		xhr.setRequestHeader("x-xsrf-token", "fregvfre=");
		xhr.send();
		xhr.onload = function() {
			//判断登录是否过期，是否报错
			// console.log(xhr)
			try {
				if (xhr.statusText == 'Unauthorized') {
					var result = {
						error: 401,
						message: '用户未登录！'
					};
					option.success(result);
					return false;
				} else {
					var data = xhr.responseText;
					if (data.indexOf('<th colspan="2"><span style="font-size: 24px">管理员登录</span></th>') > -
						1) {
						option.success({
							error: 401,
							message: '用户未登录！'
						});
						return false;
					} else if (data.indexOf('Error report</title>') > -1) {
						option.success({
							error: 500,
							message: xhr.responseText
						});
						return false;
					}
				}
			} catch (e) {
				console.log(e)
			}
			if (option.dataType == 'json') {
				if (xhr.responseJSON) {
					option.success(xhr.responseJSON)
				} else {
					try {
						var data = JSON.parse(xhr.responseText);
						option.success(data);
					} catch (e) {
						try {
							var data;
							if (typeof xhr.response == 'object') {
								data = xhr.response;
							} else {
								data = JSON.parse(xhr.response);
							}
							option.success(data);
						} catch (e) {
							try {
								option.success({
									error: 500,
									message: xhr.responseText
								});
							} catch (e) {
								try {
									option.success({
										error: 500,
										message: xhr.response
									});
								} catch (e) {
									option.success({
										error: 500,
										message: '不支持的返回类型！'
									});
								}
							}
						}
					}
				}
			} else if (option.dataType == 'text') {
				if (xhr.responseText == undefined) {
					option.success({
						error: 500,
						message: '不支持的返回类型！'
					})
				} else {
					option.success(xhr.responseText)
				}
			} else if (option.dataType == 'xml') {
				if (xhr.responseXML == undefined) {
					option.success({
						error: 500,
						message: xhr.responseText
					})
				} else {
					option.success(xhr.responseXML)
				}
			} else if (option.dataType == 'html') {
				if (xhr.responseHTML == undefined) {
					option.success({
						error: 500,
						message: xhr.responseText
					})
				} else {
					option.success(xhr.responseHTML)
				}
			} else if (option.dataType == 'blob') {
				if (xhr.response == undefined) {
					option.success({
						error: 500,
						message: xhr.responseText
					})
				} else {
					option.success(xhr.response)
				}
			}
		}
		xhr.onerror = function(e) {
			console.log('请求失败' + e);
			option.error({
				error: 500,
				message: '请求失败' + e
			});
		}
	},
	/**
	 * @des 通用方法 创建XMLhttpRequest
	 * @return xhr
	 * */
	createXHR: function() {
		if (XMLHttpRequest) {
			return new XMLHttpRequest();
		} else if (ActiveXObject) {
			if (typeof arguments.callee.activeXString != 'string') {
				var versions = ['MSXML2.XMLHttp.6.0', 'MSXML2.XMLHttp', 'MSXML2.XMLHttp.3.0'];
				for (var i = 0; i < versions.length; i++) {
					try {
						new ActiveXObject(versions[i]);
						arguments.callee.activeXString = versions[i];
						break;
					} catch (e) {
						console.log('no');
					}
				}
			}
			return new ActiveXObject(arguments.callee.activeXString);
		} else {
			throw new Error("No XHR object available");
		}
	},
	URLencode: function(sStr) {
		// sStr = escape(sStr);
		// sStr = encodeURI(sStr);
		sStr = encodeURIComponent(sStr);
		sStr = sStr.replace(/\+/g, '%2B').replace(/\"/g, '%22').replace(/\'/g, '%27').replace(/\//g,
			'%2F');
		// return encodeURI(sStr);
		// return encodeURIComponent(sStr)
		return sStr;
	},
	postXMLStream: function(option){
		var xhr = this.createXHR();
		xhr.open("POST", option.url, true);
		xhr.setRequestHeader('Content-Type',  'application/xml');
		xhr.send(option.data);
		if(typeof option.success == "function"){
			var successFuc = option.success;
			option.success = function(data){
				if(typeof GisServer == 'function' && typeof GisServer.prototype.jumpLogin == "function"){
					GisServer.prototype.jumpLogin(data, successFuc);
				} else {
					successFuc(data);
				}
			}
		}
		xhr.onload  = function() {
			if (xhr.status  === 200) {
				// 服务器响应成功，可以在这里处理响应的数据
				try {
					if (xhr.statusText == 'Unauthorized') {
						var result = {
							error: 401,
							message: '用户未登录！'
						};
						option.success(result);
						return false;
					} else {
						var data = xhr.responseText;
						if (data.indexOf(
							'<th colspan="2"><span style="font-size: 24px">管理员登录</span></th>') > -1) {
							option.success({
								error: 401,
								message: '用户未登录！'
							});
							return false;
						} else if (data.indexOf('Error report</title>') > -1) {
							option.success({
								error: 500,
								message: xhr.responseText
							});
							return false;
						}
					}
				} catch (e) {
					console.log(e)
				}
				option.success(xhr.responseText);
			} else {
				// 服务器响应失败，可以在这里处理错误
				// console.error('Request  failed.  Returned status of ' + xhr.status);
			}
		};
		xhr.onerror  = function(e) {
			option.error({
				error: 500,
				message: '请求失败' + e
			});
		};
	}
}
