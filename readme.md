



# GisServer接口操作工具包说明

## 1、背景

​		本工具包是为解决操作GisServer接口需要频繁支持的、方便GisServer前端升级和方便客户做针对GisServer前端的定制开发而应运而生。

## 2、功能介绍

​		本工具包为JavaScript版本，只为方便前端操作GisServer后台接口。

## 3、快速上手

### 3.1引入工具包

```javascript
<script src="lib/GisServer/GisServer.min.js" type="text/javascript" charset="utf-8"></script>
```

### 3.2 初始化

```javascript
//初始化时调用
var gisServer = new GisServer(window.location.origin + '/gisserver');
```

### 3.3调用

```javascript
//登录
gisServer.login(userName,password,function (data){
    if (data.sessionid) {
        window.location.href = 'index.html';
    } else {
        alert('您输入的用户名或密码错误！');
    }
});
```

## 4、接口详情

### 4.1GisServer：gisserver接口调用工具包类

```javascript
var gisserver = new GisServer(url)
```

参数说明：

url : GisServer服务器地址

内部成员

|    成员名称    |     值类型     |        简介         |
| :------------: | :------------: | :-----------------: |
|      url       |     String     | GisServer服务器地址 |
| serviceManager | ServiceManager | 服务管理实例化对象  |
|    services    |    Services    |   服务实例化对象    |
|    Version     |     String     |    版本号 1.0.0     |

成员函数：

#### login：登录接口

```javascript
gisServer.user.login(username, password, callback);
```

参数说明：

|       键        | 必须 |  值类型  |   简介   |
| :-------------: | :--: | :------: | :------: |
|    username     |  √   |  String  |  用户名  |
|    password     |  √   |  String  |   密码   |
| callback（res） |  √   | Function | 回调函数 |

res：（json）用于接收成功返回值的形参 

|    键     | 值类型 |                          简介                           |
| :-------: | :----: | :-----------------------------------------------------: |
| sessionid | String | 用户登陆成功生成的sessionid。存在登录成功，没有登录失败 |

------

#### changePassWord：修改密码接口

```javascript
gisServer.user.changePassWord(option);
```

参数说明：

option json参数

|       键        | 必须 |  值类型  |   简介   |
| :-------------: | :--: | :------: | :------: |
|     oldPwd      |  √   |  String  |  原密码  |
|     newPwd      |  √   |  String  |  新密码  |
|   confirmPwd    |  √   |  String  | 确认密码 |
| callback（res） |  √   | Function | 回调函数 |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                           返回消息                           |

------

#### logout：登出接口

```javascript
gisServer.user.logout(callback);
```

参数说明：

option json参数

|       键        | 必须 |  值类型  |   简介   |
| :-------------: | :--: | :------: | :------: |
| callback（res） |  √   | Function | 回调函数 |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                           返回消息                           |

------

#### getService：获取服务接口

```
gisServer.getService(name, type)
```

参数说明：

name：服务名称

type：服务接口类型   可取值'OGC:WMS'、'OGC:WFS'、'OGC:WMTS'、'OGC:3DTILE'、'OSGEO:TMS'、'AgsREST:MapServer'、'AgsREST:FeatureServer'、'AgsREST:GeometryServer'、'AgsREST:imageServer'、'SE:Geocode'、'SE:Data'、'SE:CTS'、'SE:WNS'、'SE:WCS'

返回值：接口对象

### 4.2 ServiceManager：服务管理

```javascript
gisserver.serviceManager
```

成员函数：

##### list：获取服务列表（不包含地理编码、几何服务、数据服务）

```javascript
gisServer.serviceManager.list(option);
```

参数说明：

option ：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
|         type         |  ×   |  String  |   服务类型   |
|        group         |  ×   |  String  |   服务分组   |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

注：type和group全部不传入返回所有结果

res：（json）用于接收成功返回值的形参

| 键      | 值类型 | 简介                                                         |
| ------- | ------ | ------------------------------------------------------------ |
| error   | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String | 对返回值的进一步解释                                         |
| servers | Array  | 服务数组                                                     |

e：网络错误异常返回值

------

##### startService:启动服务

```javascript
gisServer.serviceManager.startService(name, successCallback, errorCallback);
```

参数说明：

| 键                   | 必须 | 值类型   | 简介         |
| -------------------- | ---- | -------- | ------------ |
| name                 | √    | String   | 服务名称     |
| successCallback(res) | √    | Function | 成功回调函数 |
| errorCallback(e)     | ×    | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参

| 键      | 值类型 | 简介                                                         |
| ------- | ------ | ------------------------------------------------------------ |
| error   | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String | 对返回值的进一步解释                                         |
| service | JSON   | 服务信息                                                     |

e：网络错误异常返回值

------

##### stopService:停止服务

```javascript
gisServer.serviceManager.stopService(name, successCallback, errorCallback);
```

参数说明：

| 键                   | 必须 | 值类型   | 简介         |
| -------------------- | ---- | -------- | ------------ |
| name                 | √    | String   | 服务名称     |
| successCallback(res) | √    | Function | 成功回调函数 |
| errorCallback(e)     | ×    | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参

| 键      | 值类型 | 简介                                                         |
| ------- | ------ | ------------------------------------------------------------ |
| error   | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String | 对返回值的进一步解释                                         |
| service | JSON   | 服务信息                                                     |

e：网络错误异常返回值

------

##### deleteService:删除服务

```javascript
gisServer.serviceManager.deleteService(name, successCallback, errorCallback);
```

参数说明：

| 键                   | 必须 | 值类型   | 简介         |
| -------------------- | ---- | -------- | ------------ |
| name                 | √    | String   | 服务名称     |
| successCallback(res) | √    | Function | 成功回调函数 |
| errorCallback(e)     | ×    | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### serviceInfo:服务信息

```javascript
gisServer.serviceManager.serviceInfo(name, successCallback, errorCallback);
```

参数说明：

| 键                   | 必须 | 值类型   | 简介         |
| -------------------- | ---- | -------- | ------------ |
| name                 | √    | String   | 服务名称     |
| successCallback(res) | √    | Function | 成功回调函数 |
| errorCallback(e)     | ×    | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参

| 键      | 值类型 | 简介                                                         |
| ------- | ------ | ------------------------------------------------------------ |
| error   | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String | 对返回值的进一步解释                                         |
| props   | JSON   | 属性                                                         |
| group   | String | 分组                                                         |

e：网络错误异常返回值

##### publishService:发布服务

```javascript
gisServer.serviceManager.publishService(name, type, option);
```

参数说明：

name：服务名称

type： 服务类型

option ：（json）传入参数

| 键                   | 必须 | 值类型       | 简介                               | wms  | wfs  | wns  | tms  | cts  | c3d单体人工模型 | c3d实例化模型 | c3d3d瓦片数据 | c3d矢量参数化建模 | wcs  |
| -------------------- | ---- | ------------ | ---------------------------------- | ---- | ---- | ---- | ---- | ---- | --------------- | ------------- | ------------- | ----------------- | ---- |
| title                | √    | String       | 服务标题                           | √    | √    | √    | √    | √    | √               | √             | √             | √                 | √    |
| group                | ×    | String       | 服务所在分组  参数不存在时为默认组 | √    | √    | √    | √    | √    | √               | √             | √             | √                 | √    |
| path                 | √    | File         | 服务所需文件                       | √    | √    | √    | ×    | ×    | ×               | ×             | ×             | ×                 | ×    |
| path或file           | √    | String或File | 切片路径或服务所需上传文件         | ×    | ×    | ×    | √    | √    | √               | √             | √             | √                 | √    |
| refine               | √    | String       | LOD策略                            | ×    | ×    | ×    | ×    | ×    | √               | √             | ×             | √                 | ×    |
| levels               | √    | String       | 楼层字段                           | ×    | ×    | ×    | ×    | ×    | ×               | ×             | ×             | √                 | ×    |
| height               | √    | Number       | 单层层高                           | ×    | ×    | ×    | ×    | ×    | ×               | ×             | ×             | √                 | ×    |
| color                | √    | String       | 建筑颜色                           | ×    | ×    | ×    | ×    | ×    | ×               | ×             | ×             | √                 | ×    |
| dataType             | √    | String       | 模型数据类型                       | ×    | ×    | ×    | ×    | ×    | √               | √             | ×             | √                 | ×    |
| successCallback(res) | √    | Function     | 成功回调函数                       | √    | √    | √    | √    | √    | √               | √             | √             | √                 | √    |
| errorCallback(e)     | ×    | Function     | 失败回调函数                       | √    | √    | √    | √    | √    | √               | √             | √             | √                 | √    |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### publishServiceByEdp:统一发布接口

用于发布wms、wfs、wns和tms服务

```
gisServer.serviceManager.publishServiceByEdp(name, type, option);
```

参数说明：

name：服务名称

type： 服务类型

option ：（json）传入参数

|          键          | 必须 |    值类型    |                             简介                             |
| :------------------: | :--: | :----------: | :----------------------------------------------------------: |
|        title         |  √   |    String    |                           服务标题                           |
|        group         |  √   |    String    |              服务所在分组  参数不存在时为默认组              |
|         path         |  √   |     File     |              wms、wfs、wns服务上传文件对应参数               |
|      path或file      |  √   | String或File |            tms服务服务器路径或者上传文件对应参数             |
|        andWFS        |  ×   |    String    |    值yes或者不传此参数。发布wms服务时是否同时发布wfs服务     |
|        andWNS        |  ×   |    String    | 值yes或者不传此参数。发布wms或者wfs服务时是否同时发布wns服务 |
| successCallback(res) |  √   |   Function   |                         成功回调函数                         |
|   errorCallback(e)   |  ×   |   Function   |                         失败回调函数                         |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### edit:编辑接口

编辑wms、wfs、wns、tms、cts、c3d、wcs服务

```
gisServer.serviceManager.edit(name, type, option);
```

参数说明：

name：服务名称

type： 服务类型

option ：（json）传入参数

| 键                   | 必须 | 值类型       | 简介                         | wms  | wms 编辑路径 | wfs  | wns  | tms  | cts  | c3d通用编辑接口 | c3d联接地址编辑接口 | c3d更新模型接口 | wcs  |
| -------------------- | ---- | ------------ | ---------------------------- | ---- | ------------ | ---- | ---- | ---- | ---- | --------------- | ------------------- | --------------- | ---- |
| title                | √    | String       | 服务标题                     | √    | ×            | √    | √    | √    | √    | √               | √                   | √               | √    |
| group                | ×    | String       | 服务所在分组                 | √    | ×            | √    | √    | √    | √    | √               | √                   | √               | √    |
| abstract             | ×    | String       | 服务简介                     | √    | ×            | ×    | ×    | √    | √    | √               | √                   | √               | √    |
| info                 | ×    | String       | 元数据信息                   | √    | ×            | √    | √    | √    | √    | √               | √                   | √               | √    |
| antialias            | ×    | String       | 反走样 值为"true"            | √    | ×            | ×    | ×    | ×    | ×    | ×               | ×                   | ×               | ×    |
| layerLimit           | √    | Number       | 最大图层数量                 | √    | ×            | ×    | ×    | ×    | ×    | ×               | ×                   | ×               | ×    |
| maxWidth             | √    | Number       | 最大图层宽度                 | √    | ×            | ×    | ×    | ×    | ×    | ×               | ×                   | ×               | ×    |
| maxHeight            | √    | Number       | 最大图层高度                 | √    | ×            | ×    | ×    | ×    | ×    | ×               | ×                   | ×               | ×    |
| maxFeature           | ×    | Number       | 最大返回结果 空值则不做修改  | √    | ×            | √    | √    | ×    | ×    | ×               | ×                   | ×               | ×    |
| tilePath             | √    | String       | 切片路径                     | ×    | √            | ×    | ×    | ×    | ×    | ×               | ×                   | ×               | ×    |
| path                 | ×    | String       | 切片路径                     | ×    | ×            | ×    | ×    | √    | √    | √               | √                   | √               | √    |
| cascade              | ×    | String       | 级联服务                     | ×    | ×            | ×    | ×    | √    | ×    | ×               | ×                   | ×               | ×    |
| offsetX              | ×    | Number       | X方向模型偏移量              | ×    | ×            | ×    | ×    | ×    | ×    | √               | √                   | ×               | ×    |
| offsetY              | ×    | Number       | Y方向模型偏移量              | ×    | ×            | ×    | ×    | ×    | ×    | √               | √                   | ×               | ×    |
| offsetZ              | ×    | Number       | Z方向模型偏移量              | ×    | ×            | ×    | ×    | ×    | ×    | √               | √                   | ×               | ×    |
| link                 | ×    | String       | 联接地址                     | ×    | ×            | ×    | ×    | ×    | ×    | ×               | √                   | ×               | ×    |
| path或file           | ×    | String或file | 服务器上文件路径或者上传文件 | ×    | ×            | ×    | ×    | ×    | ×    | ×               | ×                   | √               | ×    |
| ids                  | ×    | String       | 要删除的ID                   | ×    | ×            | ×    | ×    | ×    | ×    | ×               | ×                   | √               | ×    |
| pattern              | ×    | String       | 含有时间序列的数据的时间格式 | ×    | ×            | ×    | ×    | ×    | ×    | ×               | ×                   | ×               | √    |
| successCallback(res) | √    | Function     | 成功回调函数                 | √    | √            | √    | √    | √    | √    | √               | √                   | √               | √    |
| errorCallback(e)     | ×    | Function     | 失败回调函数                 | √    | √            | √    | √    | √    | √    | √               | √                   | √               | √    |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

### 4.3 OGC接口

#### 4.3.1 WMS

```
var wmsService = new OGC.WMS(uri);//直接调用
var wmsService = gisServer.getService(name, 'OGC:WMS');//通过GisServer调用
```

参数说明：

name：服务名称

uri：服务地址

##### GetCapabilities：获取服务中的要素类及支持的操作

```js
wmsService.GetCapabilities(option);//通过GisServer调用
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

##### GetMap：获取地图

```js
wmsService.GetMap(option);
```

参数说明：

option：（json）传入参数

| 键                   | 必须 | 值类型   | 简介                                                         |
| -------------------- | ---- | -------- | ------------------------------------------------------------ |
| layers               | ×    | String   | 图层名<br />默认值为空，<br />填写图层名，多个图层名用 , （英文状态逗号）隔开 |
| crs                  | ×    | String   | 地图坐标系                                                   |
| bbox                 | √    | String   | 区域坐标范围（minx,miny,maxx,maxy）                          |
| width                | √    | Number   | 生成图片宽度                                                 |
| height               | √    | Number   | 生成图片高度                                                 |
| format               | √    | String   | 生成图片的格式<br />可选值为 image/jpeg或image/png或image/gif |
| transparent          | ×    | String   | 是否透明<br />可选值true或false                              |
| successCallback(res) | √    | Function | 成功回调函数                                                 |
| errorCallback(e)     | ×    | Function | 失败回调函数                                                 |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |      0、返回正常<br />其他       |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

##### GetFeatureInfo：获取要素信息

```js
gisServer.services.wms.OGC.GetFeatureInfo(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                          简介                          |
| :------------------: | :--: | :------: | :----------------------------------------------------: |
|         crs          |  ×   |  String  |                       地图坐标系                       |
|         bbox         |  √   |  String  |          区域坐标范围（minx,miny,maxx,maxy）           |
|        width         |  √   |  Number  |                  图片宽度（单位：px）                  |
|        height        |  √   |  Number  |                  图片高度（单位：px）                  |
|     query_layers     |  √   |  String  |                         图层名                         |
|     info_format      |  √   |  String  | 返回结果格式<br />可选值为：text/xml或application/json |
|          i           |  √   |  Number  |           像素点距离图片左侧距离（单位：px）           |
|          j           |  √   |  Number  |           像素点距离图片上侧距离（单位：px）           |
| successCallback(res) |  √   | Function |                      成功回调函数                      |
|   errorCallback(e)   |  ×   | Function |                      失败回调函数                      |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |      0、返回正常<br />其他       |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

#### 4.3.2 WFS

```
var wfsService = new OGC.WFS(uri);//直接调用
var wfsService = gisServer.getService(name, 'OGC:WFS');//通过GisServer调用
```

参数说明：

name：服务名称

uri：服务地址

##### GetCapability：获取服务中的要素类及支持的操作

```js
wfsService.GetCapabilities(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

##### DescribeFeatureType：获取要素描述信息

```js
wfsService.DescribeFeatureType(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                  简介                  |
| :------------------: | :--: | :------: | :------------------------------------: |
|       typename       |  ×   |  String  | 查询要素类型，值为空时查询所有要素类型 |
| successCallback(res) |  √   | Function |              成功回调函数              |
|   errorCallback(e)   |  ×   | Function |              失败回调函数              |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

##### GetfeatureByFeatureid:通过要素id搜索要素

```
wfsService.GetfeatureByFeatureid(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                        简介                         |
| :------------------: | :--: | :------: | :-------------------------------------------------: |
|       typename       |  √   |  String  |       查询要素类型，值为空时查询所有要素类型        |
|     propertyname     |  √   |  String  | 想要查询的属性名  多个属性名用,（英文状态逗号）隔开 |
|      featureid       |  √   |  String  |   要查询的要素id，多个id间用,（英文状态逗号）隔开   |
|        format        |  ×   |  String  |    返回数据格式<br />默认格式为xml，支持json格式    |
| successCallback(res) |  √   | Function |                    成功回调函数                     |
|   errorCallback(e)   |  ×   | Function |                    失败回调函数                     |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

##### GetfeatureByBbox:通过范围搜索要素

```
wfsService.GetfeatureByBbox(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                        简介                         |
| :------------------: | :--: | :------: | :-------------------------------------------------: |
|       typename       |  √   |  String  |       查询要素类型，值为空时查询所有要素类型        |
|     propertyname     |  √   |  String  | 想要查询的属性名  多个属性名用,（英文状态逗号）隔开 |
|         bbox         |  √   |  String  |                        范围                         |
|        format        |  ×   |  String  |    返回数据格式<br />默认格式为xml，支持json格式    |
| successCallback(res) |  √   | Function |                    成功回调函数                     |
|   errorCallback(e)   |  ×   | Function |                    失败回调函数                     |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

##### GetfeatureByFilter:通过自定义筛选条件搜索要素

```
wfsService.GetfeatureByFilter(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                        简介                         |
| :------------------: | :--: | :------: | :-------------------------------------------------: |
|       typename       |  √   |  String  |       查询要素类型，值为空时查询所有要素类型        |
|     propertyname     |  √   |  String  | 想要查询的属性名  多个属性名用,（英文状态逗号）隔开 |
|        filter        |  √   |  String  |                      筛选条件                       |
|        format        |  ×   |  String  |    返回数据格式<br />默认格式为xml，支持json格式    |
| successCallback(res) |  √   | Function |                    成功回调函数                     |
|   errorCallback(e)   |  ×   | Function |                    失败回调函数                     |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

##### Transaction：编辑要素

```
wfsService.Transaction(option);
```

参数说明:

option：（json）传入参数

|          键          | 必须 |  值类型  |       简介       |
| :------------------: | :--: | :------: | :--------------: |
|         req          |  √   |  String  | 对要素进行的操作 |
| successCallback(res) |  √   | Function |   成功回调函数   |
|   errorCallback(e)   |  ×   | Function |   失败回调函数   |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

#### 4.3.3 WMTS

```
var wmtsService = new OGC.WMTS(uri);//直接调用
var wmtsService = gisServer.getService(name, 'OGC:WMTS');//通过GisServer调用
```

参数说明：

name：服务名称

uri：服务地址

##### GetCapabilities：切片地图Web服务

```js
wmtsService.GetCapabilities(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

##### GetTile：获取地图切片服务

```js
wmtsService.GetTile(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
|      TileMatrix      |  √   |  Number  |   瓦片矩阵   |
|       TileRow        |  √   |  Number  |   瓦片行数   |
|       TileCol        |  √   |  Number  |   瓦片列数   |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

#### 4.3.4 3dtile

```参数说明：
var a3dtileService = new OGC.3DTILE(uri);//直接调用
var a3dtileService = gisServer.getService(name, 'OGC:3dtile');//通过GisServer调用
```

参数说明：

name：服务名称

uri：服务地址

##### C3dService：获取服务中的要素类及支持的操作

```js
a3dtileService.C3dService(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参 

e：网络错误异常返回值

------

### 4.4 OSGEO接口

#### 4.4.1 TMS

```
var tmsService = new OSGEO.TMS(uri);//直接调用
var tmsService = gisServer.getService(name, 'OSGEO:TMS');//通过GisServer调用
```

参数说明：

name：服务名称

uri：服务地址

###### TileMap：获取元数据服务

```js
tmsService.TileMap(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

###### Tile：获取切片图服务

```js
tmsService.Tile(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
|        level         |  √   |  Number  |   切片级数   |
|          x           |  √   |  Number  |  切片x坐标   |
|          y           |  √   |  Number  |  切片y坐标   |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------



### 4.5 Ags REST接口

#### 4.5.1MapServer服务

```
var mapServerService = new AgsREST.MapServer(uri);//直接调用
var mapServerService = gisServer.getService(name, 'AgsREST:MapServer');//通过GisServer调用
```

参数说明：

name：服务名称

uri：服务地址

##### mapService：获取元数据信息

```js
mapServerService.mapService(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
|         name         |  √   |  String  |   服务名称   |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### exportMap：导出地图

```js
mapServerService.exportMap(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                             简介                             |
| :------------------: | :--: | :------: | :----------------------------------------------------------: |
|         bbox         |  √   |  String  |            导出图像的范围（minx,miny,maxx,maxy）             |
|        bboxSR        |  ×   |  String  |                         bbox的坐标系                         |
|         size         |  ×   |  String  |             导出图像的尺寸，默认值为400×400像素              |
|        format        |  ×   |  String  |               导出图像的格式，默认值为.png格式               |
|        layers        |  ×   |  String  | 要对其执行识别操作的图层（默认值为top），取值top,visible,all |
|     transparent      |  ×   | Boolean  | 如果为true，则导出图像时地图的背景色设置为透明色，默认值为false |
|       imageSR        |  ×   |  String  |                       导出图像的坐标系                       |
|          f           |  ×   |  String  | 结果返回方式，默认值为默认响应格式为html，取值json,image,html,kmz |
| successCallback(res) |  √   | Function |                         成功回调函数                         |
|   errorCallback(e)   |  ×   | Function |                         失败回调函数                         |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |      0、返回正常<br />其他       |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

##### Identify：识别（图像服务）

```js
mapServerService.Identify(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                             简介                             |
| :------------------: | :--: | :------: | :----------------------------------------------------------: |
|       geometry       |  √   |  String  |                  定义要识别的位置的几何图形                  |
|     geometryType     |  √   |  String  |          几何参数指定的几何类型（可以是点或多边形）          |
|        layers        |  ×   |  String  | 要对其执行识别操作的图层（默认值为top），取值top,visible,all |
|      tolerance       |  √   |  Number  | 屏幕像素与应执行识别的指定几何图形的距离，容差的值是一个整数 |
|      mapExtent       |  √   |  String  |   当前正在查看的地图的范围或边界框（xmin,ymin,xmax,ymax）    |
|     imageDisplay     |  √   |  String  | 当前查看的地图的屏幕图像显示参数（宽、高、DPI）示例：imageDisplay=600,550,96 |
|    returnGeometry    |  ×   | Boolean  | 如果为true，则结果集将包括与每个结果关联的几何图形，默认值为true |
| successCallback(res) |  √   | Function |                         成功回调函数                         |
|   errorCallback(e)   |  ×   | Function |                         失败回调函数                         |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### Find：查找指定结果

```js
mapServerService.Find(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                             简介                             |
| :------------------: | :--: | :------: | :----------------------------------------------------------: |
|      searchText      |  √   |  String  |                在指定的图层和字段中搜索的文本                |
|       contains       |  √   | Boolean  | 如果为false，则该操作将搜索searchText字符串的完全匹配，且区分大小写。否则，它会搜索包含提供的searchText的值，且不区分大小写。默认值为true |
|     searchFields     |  ×   |  String  | 要搜索的字段的名称。这些字段被指定为以逗号分隔的字段名称列表，如果未指定此参数，则搜索所有字段 |
|        layers        |  √   |  String  | 要对其执行查找操作的图层，语法：layers=<layerId1>,<layerId2> |
|    returnGeometry    |  ×   | Boolean  | 如果为true，则结果集将包括与每个结果关联的几何图形，默认值为true |
| successCallback(res) |  √   | Function |                         成功回调函数                         |
|   errorCallback(e)   |  ×   | Function |                         失败回调函数                         |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### layerMetadata：获取图层数据信息

```js
mapServerService.layerMetadata(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
|     layerAddress     |  √   |  String  |   选定图层   |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### Query：查询

```js
mapServerService.Query(option)
```

参数说明：

option：（json）传入参数

|             键             | 必须 |  值类型  |                             简介                             |
| :------------------------: | :--: | :------: | :----------------------------------------------------------: |
|        layerAddress        |  √   |  String  |                           选定图层                           |
|          geometry          |  √   |  String  |                  定义要识别的位置的几何图形                  |
|        geometryType        |  √   |  String  | 几何参数指定的几何类型，默认类型为包络（可以是包络、点、线或多边形） |
|         spatialRel         |  √   |  String  | 执行查询时要应用于输入几何的空间关系。支持的空间关系包括相交、包络相交等。默认空间关系是相交 |
|           where            |  ×   |  String  |                String查询过滤器的 WHERE 子句                 |
|         objectIds          |  ×   |  String  | 要查询的该图层或表的对象ID，语法：objectIds=<objectId1>, <objectId2> |
|         outFields          |  ×   |  String  |                    返回结果集中的字段列表                    |
|       returnGeometry       |  ×   | Boolean  | 如果为true，则结果集将包括与每个结果关联的几何图形，默认值为true |
|      returnCountOnly       |  ×   | Boolean  | 如果为true，则响应仅包括查询将返回的计数（特征/记录数），否则响应是一个特征集。默认值为false |
|       outStatistics        |  ×   |  String  |          要计算的一个或多个基于字段的统计数据的定义          |
| groupByFieldsForStatistics |  ×   |  String  |      使用需要分组以计算统计信息的值的一个或多个字段名称      |
|    successCallback(res)    |  √   | Function |                         成功回调函数                         |
|      errorCallback(e)      |  ×   | Function |                         失败回调函数                         |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### TileMap：地图切片

```js
mapServerService.TileMap(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
|      TileMatrix      |  √   |  Number  |     矩阵     |
|       TileRow        |  √   |  Number  |      行      |
|       TileCol        |  √   |  Number  |      列      |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |      0、返回正常<br />其他       |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------



#### 4.5.2 FeatureServer

```
var featureServerService = new AgsREST.FeatureServer(uri);//直接调用
var mapServerService = gisServer.getService(name, 'AgsREST:Feature');//通过GisServer调用
```

参数说明：

name：服务名称

uri：服务地址

##### FeatureService：获取元数据信息

```js
featureServerService.FeatureService(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### Query：查询

```js
featureServerService.Query(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                             简介                             |
| :------------------: | :--: | :------: | :----------------------------------------------------------: |
|      layerDefs       |  √   |  String  | 图层定义，允许通过为这些图层指定定义表达式（where 子句）来过滤查询中各个图层的特征 |
|       geometry       |  √   |  String  |                  定义要识别的位置的几何图形                  |
|     geometryType     |  √   |  String  | 几何参数指定的几何类型，默认类型为包络（可以是包络、点、线或多边形） |
|      spatialRel      |  √   |  String  | 执行查询时要应用于输入几何的空间关系，支持的空间关系包括相交、包含、包络相交、内部等。默认空间关系是相交 |
|    returnGeometry    |  ×   | Boolean  | 如果为true，则结果集将包括与每个结果关联的几何图形，默认值为true |
|   returnCountOnly    |  ×   | Boolean  | 如果为true，则响应仅包括查询将返回的计数（特征/记录数）。否则响应是一个特征集。默认值为false |
| successCallback(res) |  √   | Function |                         成功回调函数                         |
|   errorCallback(e)   |  ×   | Function |                         失败回调函数                         |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### QueryLayer：查询

```js
featureServerService.QueryLayer(option)
```

参数说明：

option：（json）传入参数

|             键             | 必须 |  值类型  |                             简介                             |
| :------------------------: | :--: | :------: | :----------------------------------------------------------: |
|        layerAddress        |  √   |  String  |                           选定图层                           |
|          geometry          |  √   |  String  |                  定义要识别的位置的几何图形                  |
|        geometryType        |  √   |  String  | 几何参数指定的几何类型，默认类型为包络（可以是包络、点、线或多边形） |
|         spatialRel         |  √   |  String  | 执行查询时要应用于输入几何的空间关系。支持的空间关系包括相交、包络相交等。默认空间关系是相交 |
|           where            |  ×   |  String  |                String查询过滤器的 WHERE 子句                 |
|         objectIds          |  ×   |  String  | 要查询的该图层或表的对象ID，语法：objectIds=<objectId1>, <objectId2> |
|         outFields          |  ×   |  String  |                    返回结果集中的字段列表                    |
|       returnGeometry       |  ×   | Boolean  | 如果为true，则结果集将包括与每个结果关联的几何图形，默认值为true |
|      returnCountOnly       |  ×   | Boolean  | 如果为true，则响应仅包括查询将返回的计数（特征/记录数）。否则响应是一个特征集。默认值为false |
|       outStatistics        |  ×   |  String  |          要计算的一个或多个基于字段的统计数据的定义          |
| groupByFieldsForStatistics |  ×   |  String  |      使用需要分组以计算统计信息的值的一个或多个字段名称      |
|    successCallback(res)    |  √   | Function |                         成功回调函数                         |
|      errorCallback(e)      |  ×   | Function |                         失败回调函数                         |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### QueryRelatedRecords：查询相关记录（功能服务）

```js
featureServerService.QueryRelatedRecords(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                             简介                             |
| :------------------: | :--: | :------: | :----------------------------------------------------------: |
|     layerAddress     |  √   |  String  |                           选定图层                           |
|      objectIds       |  √   |  String  | 要查询的该图层或表的对象ID，语法：objectIds=<objectId1>, <objectId2> |
|    relationshipId    |  √   |  String  |                        要查询的关系ID                        |
|      outFields       |  ×   |  String  |                    返回结果集中的字段列表                    |
| definitionExpression |  ×   |  String  | 要应用于图层的定义表达式，只有符合这个表达式的记录才会被返回，示例：定义表达式=POP2000 > 100000 |
|    returnGeometry    |  ×   | Boolean  | 如果为true，则结果集将包括与每个结果关联的几何图形，默认值为true |
| successCallback(res) |  √   | Function |                         成功回调函数                         |
|   errorCallback(e)   |  ×   | Function |                         失败回调函数                         |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### AddFeatures：添加要素

```js
featureServerService.AddFeatures(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |         简介         |
| :------------------: | :--: | :------: | :------------------: |
|     layerAddress     |  √   |  String  |       选定图层       |
|       features       |  √   |  String  | 要添加的要素功能数组 |
| successCallback(res) |  √   | Function |     成功回调函数     |
|   errorCallback(e)   |  ×   | Function |     失败回调函数     |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### UpdateFeatures：更新要素

```js
featureServerService.UpdateFeatures(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |         简介         |
| :------------------: | :--: | :------: | :------------------: |
|     layerAddress     |  √   |  String  |       选定图层       |
|       features       |  √   |  String  | 要更新的要素功能数组 |
| successCallback(res) |  √   | Function |     成功回调函数     |
|   errorCallback(e)   |  ×   | Function |     失败回调函数     |

res：（string）用于接收成功返回值的形参 o

e：网络错误异常返回值

------

##### DeleteFeatures：删除要素

```js
featureServerService.DeleteFeatures(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                             简介                             |
| :------------------: | ---- | :------: | :----------------------------------------------------------: |
|     layerAddress     | √    |  String  |                           选定图层                           |
|      objectIds       | √    |  String  | 要删除的图层或表的对象ID，语法：objectIds=<objectId1>, <objectId2>（objectIds or where must input） |
|        where         | √    |  String  | 查询过滤器的 where 子句，允许对图层中的字段进行任何合法的 SQL where 子句，符合指定 where 子句的特征将被删除（objectIds or where must input） |
| successCallback(res) | √    | Function |                         成功回调函数                         |
|   errorCallback(e)   | ×    | Function |                         失败回调函数                         |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### layer：获取图层数据信息

```js
featureServerService.layer(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
|     layerAddress     |  √   |  String  |   选定图层   |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### apply edits：图层要素的增删改编译

```
featureServerService.applyEdits(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
|         adds         |  ×   |  String  | 要添加的要素 |
|       updates        |  ×   |  String  |  修改的要素  |
|       deletes        |  ×   |  String  | 删除要素的id |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

#### 4.5.3 imageServer

```
var imageServerService = new AgsREST.ImageServer(uri);//直接调用
var imageServerService = gisServer.getService(name, 'AgsREST:Image');//通过GisServer调用
```

参数说明：

name：服务名称

uri：服务地址

##### mapService：获取元数据信息

```js
imageServerService.mapService(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### TileMap：地图切片

```js
imageServerService.TileMap(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
|      TileMatrix      |  √   |  Number  |     矩阵     |
|       TileRow        |  √   |  Number  |      行      |
|       TileCol        |  √   |  Number  |      列      |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |      0、返回正常<br />其他       |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

#### 4.5.4 GeometryServer

```
var geometryServerService = new AgsREST.GeometryServer(name, url);//直接调用
var geometryServerService = gisServer.getService(name, 'AgsREST:GeometryServer');//通过GisServer调用
```

参数说明：

name：服务名称

url：服务地址

##### geometryService：几何服务

```javascript
geometryServerService.geometryService(option);
```

参数说明：

option：（json）传入参数

|       键        | 必须 |  值类型  |     简介     |
| :-------------: | :--: | :------: | :----------: |
| successCallback |  √   | Function | 成功回调函数 |
|  errorCallback  |  √   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### areasAndLengths：计算面积和长度

```javascript
geometryServerService.areasAndLengths(option);
```

参数说明：

option：（json）传入参数

|       键        | 必须 |  值类型  |     简介     |
| :-------------: | :--: | :------: | :----------: |
|    polygons     |  √   |  String  |    多边形    |
|       sr        |  √   |  String  |    坐标系    |
|   lengthUnit    |  √   |  String  |   长度单位   |
|    areaUnit     |  √   |  String  |   面积单位   |
|  unionResults   |  √   |  String  | 是否组合结果 |
| successCallback |  √   | Function | 成功回调函数 |
|  errorCallback  |  √   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### buffer：缓冲分析

```javascript
geometryServerService.buffer(option);
```

参数说明：

option：（json）传入参数

|       键        | 必须 |  值类型  |        简介        |
| :-------------: | :--: | :------: | :----------------: |
|   geometries    |  √   |  String  | 输入几何，可以多个 |
|      inSR       |  √   |  String  |     输入坐标系     |
|      outSR      |  √   |  String  |     输出坐标系     |
|    bufferSR     |  √   |  String  |     缓冲坐标系     |
|    distances    |  √   |  String  |      缓冲距离      |
|      unit       |  √   |  String  |        单位        |
|  unionResults   |  √   |  String  |    是否组合结果    |
| successCallback |  √   | Function |    成功回调函数    |
|  errorCallback  |  √   | Function |    失败回调函数    |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### convexHull：获取凸包算法结果

```javascript
geometryServerService.convexHull(option);
```

参数说明：

option：（json）传入参数

|       键        | 必须 |  值类型  |       简介       |
| :-------------: | :--: | :------: | :--------------: |
|   geometries    |  √   |  String  | 一个或者多个几何 |
|       sr        |  √   |  String  |      坐标系      |
| successCallback |  √   | Function |   成功回调函数   |
|  errorCallback  |  √   | Function |   失败回调函数   |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### difference：不相交部分

```javascript
geometryServerService.difference(option);
```

参数说明：

option：（json）传入参数

|       键        | 必须 |  值类型  |       简介       |
| :-------------: | :--: | :------: | :--------------: |
|   geometries    |  √   |  String  | 一个或者多个几何 |
|    geometry     |  √   |  String  |     一个几何     |
|       sr        |  √   |  String  |      坐标系      |
| successCallback |  √   | Function |   成功回调函数   |
|  errorCallback  |  √   | Function |   失败回调函数   |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### distance：不相交部分

```javascript
geometryServerService.distance(option);
```

参数说明：

option：（json）传入参数

|       键        | 必须 |  值类型  |     简介     |
| :-------------: | :--: | :------: | :----------: |
|    geometry1    |  √   |  String  |    几何1     |
|    geometry2    |  √   |  String  |    几何2     |
|       sr        |  √   |  String  |    坐标系    |
|  distanceUnit   |  √   |  String  |   距离单位   |
|    geodesic     |  √   |  String  |   是否测地   |
| successCallback |  √   | Function | 成功回调函数 |
|  errorCallback  |  √   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### Intersect：相交部分

```javascript
geometryServerService.intersect(option);
```

参数说明：

option：（json）传入参数

|       键        | 必须 |  值类型  |       简介       |
| :-------------: | :--: | :------: | :--------------: |
|   geometries    |  √   |  String  | 一个或者多个几何 |
|    geometry     |  √   |  String  |       几何       |
|       sr        |  √   |  String  |      坐标系      |
| successCallback |  √   | Function |   成功回调函数   |
|  errorCallback  |  √   | Function |   失败回调函数   |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### labelPoints：获取文本位置

```javascript
geometryServerService.labelPoints(option);
```

参数说明：

option：（json）传入参数

|       键        | 必须 |  值类型  |        简介        |
| :-------------: | :--: | :------: | :----------------: |
|    polygons     |  √   |  String  | 一个或者多个多边形 |
|       sr        |  √   |  String  |       坐标系       |
| successCallback |  √   | Function |    成功回调函数    |
|  errorCallback  |  √   | Function |    失败回调函数    |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### lengths：获取线段长度

```javascript
geometryServerService.lengths(option);
```

参数说明：

option：（json）传入参数

|       键        | 必须 |  值类型  |       简介       |
| :-------------: | :--: | :------: | :--------------: |
|    polylines    |  √   |  String  | 一个或者多个线段 |
|       sr        |  √   |  String  |      坐标系      |
|   lengthUnit    |  √   |  String  |     长度单位     |
|    geodesic     |  √   |  String  |     是否测地     |
| successCallback |  √   | Function |   成功回调函数   |
|  errorCallback  |  √   | Function |   失败回调函数   |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### project：坐标系转换

```javascript
geometryServerService.project(option);
```

参数说明：

option：（json）传入参数

|       键        | 必须 |  值类型  |       简介       |
| :-------------: | :--: | :------: | :--------------: |
|   geometries    |  √   |  String  | 一个或者多个线段 |
|      inSR       |  √   |  String  |  输入数据坐标系  |
|      outSR      |  √   |  String  |  输出数据坐标系  |
| successCallback |  √   | Function |   成功回调函数   |
|  errorCallback  |  √   | Function |   失败回调函数   |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### relation：判断几何关系

```javascript
geometryServerService.relation(option);
```

参数说明：

option：（json）传入参数

|       键        | 必须 |  值类型  |                             简介                             |
| :-------------: | :--: | :------: | :----------------------------------------------------------: |
|   geometries1   |  √   |  String  |                      一个或者多个几何1                       |
|   geometries2   |  √   |  String  |                      一个或者多个几何2                       |
|       sr        |  √   |  String  |                            坐标系                            |
|    relation     |  √   |  String  | 几何关系，包括Cross、Disjoint、In、Interior Intersection、Intersection、Line Coincidence、Line Touch、Overlap、Point Touch、Touch、Within |
| successCallback |  √   | Function |                         成功回调函数                         |
|  errorCallback  |  √   | Function |                         失败回调函数                         |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### simplify：求外接框

```javascript
geometryServerService.simplify(option);
```

参数说明：

option：（json）传入参数

|       键        | 必须 |  值类型  |       简介       |
| :-------------: | :--: | :------: | :--------------: |
|   geometries    |  √   |  String  | 一个或者多个几何 |
|       sr        |  √   |  String  |      坐标系      |
| successCallback |  √   | Function |   成功回调函数   |
|  errorCallback  |  √   | Function |   失败回调函数   |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### union：合并几何

```javascript
geometryServerService.union(option);
```

参数说明：

option：（json）传入参数

|       键        | 必须 |  值类型  |       简介       |
| :-------------: | :--: | :------: | :--------------: |
|   geometries    |  √   |  String  | 一个或者多个几何 |
|       sr        |  √   |  String  |      坐标系      |
| successCallback |  √   | Function |   成功回调函数   |
|  errorCallback  |  √   | Function |   失败回调函数   |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

### 4.6 SE接口（自定义接口）

#### 4.6.1 Geocode

地理编码服务rest

```
var geocodeService = new SE.GeocodeService(name, url);//直接调用
var geocodeService = gisServer.getService(name, 'SE:Geocode');//通过GisServer调用
```

参数说明：

name：服务名称

url：服务地址

##### findAddressCandidates：获取地图

```js
geocodeService.findAddressCandidates(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |         简介         |
| :------------------: | :--: | :------: | :------------------: |
|      singleLine      |  ×   |  String  |         地址         |
|     maxLocations     |  ×   |  String  |   返回最大位置数量   |
|      outFields       |  √   |  String  |        输出列        |
|       location       |  √   |  Number  |       位置信息       |
|       distance       |  √   |  Number  |         距离         |
|       category       |  √   |  String  | category地址类型名称 |
| successCallback(res) |  √   | Function |     成功回调函数     |
|   errorCallback(e)   |  ×   | Function |     失败回调函数     |

res：（String）用于接收成功返回值的形参 

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### geocodeAddresses：获取地图

```js
geocodeService.geocodeAddresses(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |         简介         |
| :------------------: | :--: | :------: | :------------------: |
|      addresses       |  ×   |  String  |         地址         |
|       category       |  ×   |  String  | category地址类型名称 |
| successCallback(res) |  √   | Function |     成功回调函数     |
|   errorCallback(e)   |  ×   | Function |     失败回调函数     |

res：（String）用于接收成功返回值的形参 

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### reverseGeocode：获取地图

```js
geocodeService.reverseGeocode(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |             简介             |
| :------------------: | :--: | :------: | :--------------------------: |
|       location       |  ×   |  String  |     地址位置，格式：x、y     |
|       distance       |  ×   |  String  | 查询范围，单位为M，缺省值为0 |
|       category       |  √   |  String  |  地址类型，缺省值为所有类型  |
| successCallback(res) |  √   | Function |         成功回调函数         |
|   errorCallback(e)   |  ×   | Function |         失败回调函数         |

res：（String）用于接收成功返回值的形参 

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------



#### 4.6.2 Data

数据服务rest

```
var dataService = new SE.DataService(name, url);//直接调用
var dataService = gisServer.getService(name, 'SE:Data');//通过GisServer调用
```

参数说明：

name：服务名称

url：服务地址

##### shapefileImport：导入shape文件入数据服务

```js
dataService.shapefileImport(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                             简介                             |
| :------------------: | :--: | :------: | :----------------------------------------------------------: |
|         url          |  √   |  String  |                           JDBC URL                           |
|        driver        |  ×   |  String  |                         Driver Class                         |
|       username       |  ×   |  String  |                         数据库用户名                         |
|       password       |  √   |  String  |                          数据库密码                          |
|    buildSpatialDB    |  √   |  String  |              如果不是空间数据库, 是否自动初始化              |
|         name         |  √   |  String  |                             表名                             |
|      shapefile       |  √   |   File   |                    shape文件里的.shp文件                     |
|      dbasefile       |  √   |   File   |                    shape文件里的.dbf文件                     |
|       charset        |  √   |  String  |                            字符集                            |
|         srs          |  √   |  String  |                            坐标系                            |
|        action        |  √   |  String  | `导入方式 create: 新建表; recreate: 如果表已存在, 则删除重建; append: 如果表已存在, 则添加新数据` |
| successCallback(res) |  √   | Function |                         成功回调函数                         |
|   errorCallback(e)   |  ×   | Function |                         失败回调函数                         |

res：（String）用于接收成功返回值的形参 

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

##### excelImport：导入shape文件入数据服务

```js
dataService.excelImport(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                             简介                             |
| :------------------: | :--: | :------: | :----------------------------------------------------------: |
|         url          |  √   |  String  |                           JDBC URL                           |
|        driver        |  ×   |  String  |                         Driver Class                         |
|       username       |  ×   |  String  |                         数据库用户名                         |
|       password       |  √   |  String  |                          数据库密码                          |
|    buildSpatialDB    |  √   |  String  |              如果不是空间数据库, 是否自动初始化              |
|         name         |  √   |  String  |                             表名                             |
|      excelfile       |  √   |   File   | excel文件，可通过字段名单元的注释进行类型定制， 格式为'M[,D]' |
|      geoFields       |  √   |   File   | Geometry数据字段。 如单字段存储，则输入该字段名，数据格式为"x1,y1,x2,y2,...";如双字段存储，则输入两个字段名，输入格式为"f_lat,f_lng" |
|       geoType        |  √   |  String  |                       Geometry数据类型                       |
|         srs          |  √   |  String  |                            坐标系                            |
|        action        |  √   |  String  | `导入方式 create: 新建表; recreate: 如果表已存在, 则删除重建; append: 如果表已存在, 则添加新数据` |
| successCallback(res) |  √   | Function |                         成功回调函数                         |
|   errorCallback(e)   |  ×   | Function |                         失败回调函数                         |

res：（String）用于接收成功返回值的形参 

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------



#### 4.6.3 wns

wns服务

```
var seWnsService = new AgsREST.WnsService(name, url);//直接调用
var seWnsService = gisServer.getService(name, 'SE:WNS');//通过GisServer调用
```

参数说明：

name：服务名称

url：服务地址

##### FindConnected：联通服务

```js
seWnsService.FindConnected(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                             简介                             |
| :------------------: | :--: | :------: | :----------------------------------------------------------: |
|         flag         |  √   |  String  | 查询要素<br />x,y 或者 featureType[.id] 不指定id表示该类型所有数据 |
|      tolerance       |  ×   |  Number  |            容忍值，当flag为x,y时有效，默认值为10             |
|       barriers       |  ×   |  String  | 障碍物<br />featureType[.id],featureType[.id] 不指定id表示该类型所有数据 |
|     featureType      |  ×   |  String  |           返回类型<br />featureType1,featureType2            |
|     propertyName     |  ×   |  String  |          返回字段<br />propertyName1,propertyName2           |
|       traceEnd       |  ×   |  String  |         是否包括停止追踪的要素<br />“true”或“false”          |
|      returnflag      |  ×   |  String  |              是否包括flag<br />“true”或“false”               |
|        format        |  ×   |  String  |            返回格式<br />gml或json<br />缺省为gml            |
| successCallback(res) |  √   | Function |                         成功回调函数                         |
|   errorCallback(e)   |  ×   | Function |                         失败回调函数                         |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

##### FindDisconnected：不联通服务

```js
seWnsService.FindDisconnected(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                             简介                             |
| :------------------: | :--: | :------: | :----------------------------------------------------------: |
|         flag         |  √   |  String  | 查询要素<br />x,y 或者 featureType[.id] 不指定id表示该类型所有数据 |
|      tolerance       |  ×   |  Number  |            容忍值，当flag为x,y时有效，默认值为10             |
|       barriers       |  ×   |  String  | 障碍物<br />featureType[.id],featureType[.id] 不指定id表示该类型所有数据 |
|     featureType      |  ×   |  String  |           返回类型<br />featureType1,featureType2            |
|     propertyName     |  ×   |  String  |          返回字段<br />propertyName1,propertyName2           |
|       traceEnd       |  ×   |  String  |       结果是否包括停止追踪的要素<br />“true”或“false”        |
|      returnflag      |  ×   |  String  |            结果是否包括flag<br />“true”或“false”             |
|        format        |  ×   |  String  |            返回格式<br />gml或json<br />缺省为gml            |
| successCallback(res) |  √   | Function |                         成功回调函数                         |
|   errorCallback(e)   |  ×   | Function |                         失败回调函数                         |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

##### FindPath：路径分析

```
seWnsService.FindPath(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                             简介                             |
| :------------------: | :--: | :------: | :----------------------------------------------------------: |
|        start         |  √   |  String  |          路径分析起点<br />x,y 或者 featureType.id           |
|         end          |  √   |  String  |          路径分析终点<br />x,y 或者 featureType.id           |
|      tolerance       |  ×   |  Number  |            容忍值，当flag为x,y时有效，默认值为10             |
|     featureType      |  ×   |  String  | 返回要素类型<br />featureType[.id],featureType[.id] 不指定id表示该类型所有数据 |
|     propertyName     |  ×   |  String  |          返回字段<br />propertyName1,propertyName2           |
|      returnflag      |  ×   |  String  |            结果是否包括flag<br />“true”或“false”             |
|        format        |  ×   |  String  |            返回格式<br />gml或json<br />缺省为gml            |
| successCallback(res) |  √   | Function |                         成功回调函数                         |
|   errorCallback(e)   |  ×   | Function |                         失败回调函数                         |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

##### FindRoute：路由分析

```js
seWnsService.FindRoute(option);
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |                   简介                    |
| :------------------: | :--: | :------: | :---------------------------------------: |
|        start         |  √   |  String  | 路由分析起点<br />x,y 或者 featureType.id |
|         end          |  √   |  String  | 路由分析终点<br />x,y 或者 featureType.id |
|      tolerance       |  ×   |  Number  |   容忍值，当flag为x,y时有效，默认值为10   |
|     propertyName     |  ×   |  String  | 返回字段<br />propertyName1,propertyName2 |
|        tatic         |  ×   |  String  |           策略<br />缺省为距离            |
|        format        |  ×   |  String  |  返回格式<br />gml或json<br />缺省为gml   |
| successCallback(res) |  √   | Function |               成功回调函数                |
|   errorCallback(e)   |  ×   | Function |               失败回调函数                |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |     0、返回正常时<br />其他      |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

------

##### FindServicearea：服务区域分析

```js
seWnsService.FindServicearea(option);
```

参数说明：

option：（json）传入参数

| 键                   | 必须 | 值类型   | 简介                                          |
| -------------------- | ---- | -------- | --------------------------------------------- |
| flag                 | √    | String   | 服务区域分析原点                              |
| breaks               | √    | Number   | 终止值，数值意义与策略相关                    |
| tolerence            | ×    | Number   | 容忍值，当flag为x,y时有效，默认值为10         |
| featureType          | ×    | String   | 返回要素类型<br />featureType1,featureType2   |
| propertyName         | ×    | String   | 返回字段                                      |
| format               | ×    | String   | 返回数据格式<br />默认格式为xml，支持json格式 |
| successCallback(res) | √    | Function | 成功回调函数                                  |
| errorCallback(e)     | ×    | Function | 失败回调函数                                  |

res：（json）用于接收成功返回值的形参 

| 键      | 值类型 | 简介                             |
| ------- | ------ | -------------------------------- |
| error   | Number | 0、返回正常时<br />其他          |
| message | String | 返回异常时，对返回值的进一步解释 |
| data    | String | 正常返回结果                     |

e：网络错误异常返回值

------



#### 4.6.4 cts

地形服务

```
var seCtsService = new SE.CTS(uri);//直接调用
var seCtsService = gisServer.getService(name, 'SE:CTS');//通过GisServer调用
```

参数说明：

name：服务名称

uri：服务地址

##### CtsService：获取服务中的要素类及支持的操作

```js
seCtsService.CtsService(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（text）用于接收成功返回值的形参

e：网络错误异常返回值

------

#### 4.6.5 c3d

模型服务

```
var seC3dService = new SE.C3D(uri);//直接调用
var seC3dService = gisServer.getService(name, 'SE:C3D');//通过GisServer调用
```

参数说明：

name：服务名称

uri：服务地址

##### C3dService：获取服务中的要素类及支持的操作

```js
seC3dService.C3dService(option)
```

参数说明：

option：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参 

e：网络错误异常返回值

------

#### 4.6.6 wcs

模型服务

```
var seWcsService = new SE.WCS(uri);//直接调用
var seWcsService = gisServer.getService(name, 'SE:WCS');//通过GisServer调用
```

参数说明：

name：服务名称

uri：服务地址

##### ServiceList：获取服务列表信息

```js
seWcsService.ServiceList(option)
```

参数说明：

option：（json）传入参数

| 键                   | 必须 | 值类型   | 简介         |
| -------------------- | ---- | -------- | ------------ |
| successCallback(res) | √    | Function | 成功回调函数 |
| errorCallback(e)     | ×    | Function | 失败回调函数 |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### ImageServer：获取元数据信息

```js
seWcsService.ImageServer(option)
```

参数说明：

option：（json）传入参数

| 键                   | 必须 | 值类型   | 简介         |
| -------------------- | ---- | -------- | ------------ |
| layerDni             | √    | String   | 服务列表值   |
| successCallback(res) | √    | Function | 成功回调函数 |
| errorCallback(e)     | ×    | Function | 失败回调函数 |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### Compute：通过计算获取数据信息

```js
seWcsService.Compute(option)
```

参数说明：

option：（json）传入参数

| 键                   | 必须 | 值类型   | 简介                                                         |
| -------------------- | ---- | -------- | ------------------------------------------------------------ |
| dataType             | √    | String   | 返回数据类型                                                 |
| expr                 | √    | String   | 计算表达式                                                   |
| args                 | √    | String   | 参与计算的参数                                               |
| bbox                 | √    | String   | 区域坐标范围（minx,miny,maxx,maxy）                          |
| size                 | ×    | String   | 导出图像的尺寸，默认值为400×400像素                          |
| imageSR              | ×    | String   | 导出图像的坐标系                                             |
| bboxSR               | ×    | String   | bbox的坐标系                                                 |
| time                 | ×    | Number   | 时间戳，单位为毫秒。如 1199145600000 (1 Jan 2008 00:00:00 GMT) |
| format               | ×    | String   | 导出图像的格式                                               |
| bandIds              | ×    | String   | 指定单个波段导出                                             |
| noData               | ×    | String   | 无信息的像素值                                               |
| f                    | √    | String   | 结果返回方式，取值json,image,html,kmz                        |
| successCallback(res) | √    | Function | 成功回调函数                                                 |
| errorCallback(e)     | ×    | Function | 失败回调函数                                                 |

res：（json）用于接收成功返回值的形参 

| 键      | 值类型 | 简介                             |
| ------- | ------ | -------------------------------- |
| error   | Number | 0、返回正常<br />其他            |
| message | String | 返回异常时，对返回值的进一步解释 |
| data    | String | 正常返回结果                     |

e：网络错误异常返回值

------

##### ExportMap：导出地图

```js
seWcsService.ExportMap(option)
```

参数说明：

option：（json）传入参数

| 键                   | 必须 | 值类型   | 简介                                                         |
| -------------------- | ---- | -------- | ------------------------------------------------------------ |
| bbox                 | √    | String   | 导出图像的范围（minx,miny,maxx,maxy）                        |
| size                 | ×    | String   | 导出图像的尺寸，默认值为400×400像素                          |
| imageSR              | ×    | String   | 导出图像的坐标系                                             |
| bboxSR               | ×    | String   | bbox的坐标系                                                 |
| time                 | ×    | Number   | 时间戳，单位为毫秒。如 1199145600000 (1 Jan 2008 00:00:00 GMT) |
| format               | ×    | String   | 导出图像的格式                                               |
| bandIds              | ×    | String   | 指定单个波段导出                                             |
| renderingRule        | ×    | String   | 渲染规则                                                     |
| noData               | ×    | String   | 无信息的像素值                                               |
| f                    | √    | String   | 结果返回方式，取值json,image,html,kmz                        |
| layerDni             | √    | String   | 服务列表值                                                   |
| successCallback(res) | √    | Function | 成功回调函数                                                 |
| errorCallback(e)     | ×    | Function | 失败回调函数                                                 |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### Identify：识别（图像服务）

```js
seWcsService.Identify(option)
```

参数说明：

option：（json）传入参数

| 键                   | 必须 | 值类型   | 简介                                                         |
| -------------------- | ---- | -------- | ------------------------------------------------------------ |
| layerDni             | √    | String   | 服务列表值                                                   |
| geometry             | √    | String   | 定义要识别的位置的几何图形                                   |
| geometryType         | √    | String   | 几何参数指定的几何类型（可以是点或多边形）                   |
| time                 | ×    | Number   | 时间戳，单位为毫秒。如 1199145600000 (1 Jan 2008 00:00:00 GMT) |
| successCallback(res) | √    | Function | 成功回调函数                                                 |
| errorCallback(e)     | ×    | Function | 失败回调函数                                                 |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### Tile：地图切片

```js
seWcsService.Tile(option)
```

参数说明：

option：（json）传入参数

| 键                   | 必须 | 值类型   | 简介         |
| -------------------- | ---- | -------- | ------------ |
| layerDni             | √    | String   | 服务列表值   |
| level                | √    | Number   | 层级         |
| row                  | √    | Number   | 行           |
| column               | √    | Number   | 列           |
| successCallback(res) | √    | Function | 成功回调函数 |
| errorCallback(e)     | ×    | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参 

| 键      | 值类型 | 简介                             |
| ------- | ------ | -------------------------------- |
| error   | Number | 0、返回正常<br />其他            |
| message | String | 返回异常时，对返回值的进一步解释 |
| data    | String | 正常返回结果                     |

e：网络错误异常返回值

------

##### keyProperties：获取关键属性

```js
seWcsService.keyProperties(option)
```

参数说明：

option：（json）传入参数

| 键                   | 必须 | 值类型   | 简介         |
| -------------------- | ---- | -------- | ------------ |
| layerDni             | √    | String   | 服务列表值   |
| successCallback(res) | √    | Function | 成功回调函数 |
| errorCallback(e)     | ×    | Function | 失败回调函数 |

res：（string）用于接收成功返回值的形参 

e：网络错误异常返回值

------

##### GetMap：获取地图

```js
seWcsService.GetMap(option);
```

参数说明：

name：（String）wcs服务名称

option：（json）传入参数

|          键          | 必须 |  值类型  |                             简介                             |
| :------------------: | :--: | :------: | :----------------------------------------------------------: |
|        layers        |  ×   |  String  | 图层名<br />默认值为空，<br />填写图层名，多个图层名用 , （英文状态逗号）隔开 |
|         crs          |  ×   |  String  |                          地图坐标系                          |
|         bbox         |  √   |  String  |             区域坐标范围（minx,miny,maxx,maxy）              |
|        width         |  √   |  Number  |                         生成图片宽度                         |
|        height        |  √   |  Number  |                         生成图片高度                         |
|        format        |  √   |  String  | 生成图片的格式<br />可选值为 image/jpeg或image/png或image/gif |
|     transparent      |  ×   |  String  |               是否透明<br />可选值true或false                |
|       imageSR        |  ×   |  String  |                        生成图像坐标系                        |
|       bandids        |  ×   |  String  |                            波段id                            |
|         time         |  ×   |  Number  | 时间戳，单位为毫秒。如 1199145600000 (1 Jan 2008 00:00:00 GMT) |
|    renderingrule     |  ×   |  String  |                           渲染规则                           |
|        nodata        |  ×   |  String  |                       无信息数据点的值                       |
| successCallback(res) |  √   | Function |                         成功回调函数                         |
|   errorCallback(e)   |  ×   | Function |                         失败回调函数                         |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |               简介               |
| :-----: | :----: | :------------------------------: |
|  error  | Number |      0、返回正常<br />其他       |
| message | String | 返回异常时，对返回值的进一步解释 |
|  data   | String |           正常返回结果           |

e：网络错误异常返回值

###  4.7Geocode服务管理

#### list：获取地理编码服务列表

```javascript
gisServer.geocodeManager.list(successCallback, errorCallback);
```

参数说明：

|       键        | 必须 |  值类型  |     简介     |
| :-------------: | :--: | :------: | :----------: |
| successCallback |  √   | Function | 成功回调函数 |
|  errorCallback  |  √   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参 

|    键     | 值类型 |                             简介                             |
| :-------: | :----: | :----------------------------------------------------------: |
|   error   | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
|  message  | String |                     对返回值的进一步解释                     |
| libraries | Object |         当error为0时，返回libraries，为正常请求结果          |

e：网络错误异常返回值

------

#### delete：删除地里编码服务（数据库）

```javascript
gisServer.geocodeManager.delete(option)
```

参数说明：

option：（json）传入参数

|       键        | 必须 |  值类型  |     简介     |
| :-------------: | :--: | :------: | :----------: |
|      name       |  √   |  String  |  数据库名称  |
| successCallback |  √   | Function | 成功回调函数 |
|  errorCallback  |  √   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参 

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

------

#### deleteCategory：根据category删除地里编码服务里的category

```javascript
gisServer.geocodeManager.deleteCategory(option);
```

参数说明：

option ：（json）传入参数

|          键          | 必须 |  值类型  |         简介         |
| :------------------: | :--: | :------: | :------------------: |
|         name         |  √   |  String  |      数据库名称      |
|       category       |  √   |  String  | category地址类型名称 |
| successCallback(res) |  √   | Function |     成功回调函数     |
|   errorCallback(e)   |  ×   | Function |     失败回调函数     |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

#### info:获取地理编码服务的信息

```javascript
gisServer.geocodeManager.info(option);
```

参数说明：

option ：（json）传入参数

|          键          | 必须 |  值类型  |     简介     |
| :------------------: | :--: | :------: | :----------: |
|         name         |  √   |  String  |  数据库名称  |
| successCallback(res) |  √   | Function | 成功回调函数 |
|   errorCallback(e)   |  ×   | Function | 失败回调函数 |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

#### publishByExcel:通过excel文件发布地理编码服务

```javascript
geocodeService.publishByExcel(option);
```

参数说明：

option ：（json）传入参数

|          键          | 必须 |  值类型  |                  简介                  |
| :------------------: | :--: | :------: | :------------------------------------: |
|         name         |  √   |  String  |               数据库名称               |
|       category       |  √   |  String  |          category地址类型名称          |
|      excelfile       |  √   |   File   |               excel文件                |
|      geoFields       |  √   |  String  |               空间信息列               |
|       geoType        |  √   |  String  | 几何类型，取值point,linestring,polygon |
| successCallback(res) |  √   | Function |              成功回调函数              |
|   errorCallback(e)   |  ×   | Function |              失败回调函数              |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

#### publishByShape:通过shape文件发布地理编码服务

```javascript
geocodeService.publishByShape(option);
```

|          键          | 必须 |  值类型  |         简介         |
| :------------------: | :--: | :------: | :------------------: |
|         name         |  √   |  String  |      数据库名称      |
|       category       |  √   |  String  | category地址类型名称 |
|      shapefile       |  √   |   File   |      shape文件       |
|      dbasefile       |  √   |  String  |       dbf文件        |
|       charset        |  √   |  String  |        字符集        |
| successCallback(res) |  √   | Function |     成功回调函数     |
|   errorCallback(e)   |  ×   | Function |     失败回调函数     |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

#### createAppendUpdateCategoryByExcel:通过exel文件创建追加更新category

```javascript
geocodeService.createAppendUpdateCategoryByExcel(option);
```

参数说明：

option ：（json）传入参数

|          键          | 必须 |  值类型  |                  简介                  |
| :------------------: | :--: | :------: | :------------------------------------: |
|         name         |  √   |  String  |               数据库名称               |
|       category       |  √   |  String  |          category地址类型名称          |
|      excelfile       |  √   |   File   |               excel文件                |
|      geoFields       |  √   |  String  |               空间信息列               |
|       geoType        |  √   |  String  | 几何类型，取值point,linestring,polygon |
|        action        |  √   |  String  |    操作，取值create、append、update    |
| successCallback(res) |  √   | Function |              成功回调函数              |
|   errorCallback(e)   |  ×   | Function |              失败回调函数              |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------

#### createAppendUpdateCategoryByShape:通过shape文件创建追加更新category

```javascript
geocodeService.createAppendUpdateCategoryByShape(option);
```

参数说明：

option ：（json）传入参数

|          键          | 必须 |  值类型  |               简介               |
| :------------------: | :--: | :------: | :------------------------------: |
|         name         |  √   |  String  |            数据库名称            |
|       category       |  √   |  String  |       category地址类型名称       |
|      shapefile       |  √   |   File   |            shape文件             |
|      dbasefile       |  √   |  String  |             dbf文件              |
|       charset        |  √   |  String  |              字符集              |
|        action        |  √   |  String  | 操作，取值create、append、update |
| successCallback(res) |  √   | Function |           成功回调函数           |
|   errorCallback(e)   |  ×   | Function |           失败回调函数           |

res：（json）用于接收成功返回值的形参

|   键    | 值类型 |                             简介                             |
| :-----: | :----: | :----------------------------------------------------------: |
|  error  | Number | 0、返回正常时<br />401、 用户未登录<br />500、 服务器内部异常<br />其他 |
| message | String |                     对返回值的进一步解释                     |

e：网络错误异常返回值

------



## 5 gisserver服务和接口关系

| 序号 |          | 接口                                   |
| ---- | -------- | -------------------------------------- |
| 1    | wms      | OGC：WMS、AgsREST:MapServer            |
| 2    | wfs      | OGC:WFS、AgsREST:FeatureServer         |
| 3    | wns      | OGC:WFS、SE:WNS                        |
| 4    | tms      | OGC:WMTS、OSGEO:TMS、AgsREST:MapServer |
| 5    | wcs      | SE:WCS                                 |
| 6    | cts      | AgsREST:MapServer、SE:CTS              |
| 7    | c3d      | OGC:3DTILE、SE:C3D                     |
| 8    | geocode  | SE:GEOCODE                             |
| 9    | data     | SE:DATA                                |
| 10   | geometry | AgsREST:GeometryServer                 |

![gisserver接口调用工具包接口说明_01](.\gisserver接口调用工具包接口说明_01.png)