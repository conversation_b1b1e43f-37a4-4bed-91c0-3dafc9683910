var OGC = {
    WMS: function (uri){
        this.uri = uri;
    },
    WFS: function (uri){
        this.uri = uri;
    },
    WMTS: function (uri){
        this.uri = uri;
    },
    D3TILE: function (uri){
        this.uri = uri;
    }
}
/**
 * @des 获取元数据服务
 * @option (json) 含有successCallback和errorCallback
 */
OGC.WMS.prototype.GetCapabilities = function(option){
    var option1 = {
        url:this.uri,
        params: {
            version: "1.3.0",
            request: "GetCapabilities",
            service: "WMS"
        },
        async: false,
        dataType: 'text',
        success: function (data) {
            var res = {};
            if (typeof option.successCallback == 'function') {
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
/**
 * @des 获取地图服务
 * @option (json) 含有layers crs bbox width height format transparent successCallback和errorCallback
 */
OGC.WMS.prototype.GetMap= function(option){
    var arr = ['layers', 'crs', 'bbox', 'width', 'height', 'format', 'transparent'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    data.version = "1.3.0";
    data.request = "GetMap";
    data.service = "WMS";
    var option1 = {
        url: this.uri,
        params: data,
        async: false,
        dataType: 'blob',
        success: function (data) {
            if (data.type == "image/jpeg" || data.type == "image/png" || data.type == "image/gif") {
                if (typeof option.successCallback == 'function') {
                    src = window.URL.createObjectURL(data);
                    var jsonObj = {};
                    jsonObj.error = 0;
                    jsonObj.data = src;
                    option.successCallback(jsonObj);

                }
            } else {
                var reader = new FileReader();
                reader.readAsText(data, 'utf-8');
                reader.onload = function (e) {
                    console.log("-----------------blobString----------------------------------------")
                    console.log(reader.result);
                    console.log("-----------------blobString----------------------------------------")
                    if (reader.result.indexOf("ServiceException") > -1) {
                        var xmlObj = string2Xml(reader.result);
                        var str = "";
                        var xmlNodesServiceException = xmlObj.querySelectorAll("ServiceException");
                        for (var i = 0; i < xmlNodesServiceException.length; i++) {

                            str += xmlNodesServiceException[i].innerHTML;
                        }
                        var jsonObj = {};
                        console.log(data)
                        jsonObj.error = 0;
                        jsonObj.message = str;
                        option.successCallback(jsonObj)
                    } else {
                        var jsonObj = {};
                        jsonObj.error = 10000;
                        jsonObj.message = reader.result;
                        option.successCallback(jsonObj)
                    }

                }
            }
        },
        error: function (data) {
            // alert("error")
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 获取要素信息
 * @option (json) 含有 crs bbox width height query_layers info_format i j successCallback和errorCallback
 */
OGC.WMS.prototype.GetFeatureInfo = function(option){
    var arr = ['crs', 'bbox', 'width', 'height', 'query_layers', 'info_format',
        'featureCount', 'i', 'j'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    data.version = "1.3.0";
    data.request = "GetFeatureinfo";
    var option1 = {
        url: this.uri,
        params: data,
        async: false,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                var res = {}
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else if (data.indexOf("ServiceException") > -1) {


                    var xmlObj = string2Xml(data);
                    var str = "";
                    var xmlNodesServiceException = xmlObj.querySelectorAll("ServiceException");
                    for (var i = 0; i < xmlNodesServiceException.length; i++) {

                        str += xmlNodesServiceException[i].innerHTML;
                    }
                    res.error = 0;
                    res.data = str;
                } else {
                    // alert(data)
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
/**
 * @des 元数据服务
 * @option (json) 含有successCallback和errorCallback
 */
OGC.WFS.prototype.GetCapabilities = function(option){
    var data = {};
    data.version = "1.3.0",
        data.request = "GetCapabilities",
        data.service = "WFS"
    var option1 = {
        url: this.uri,
        params: data,
        async: false,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                var res = {};
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
/**
 * @des 获取要素描述信息
 * @option (json) 含有typename successCallback和errorCallback
 */
OGC.WFS.prototype.DescribeFeatureType = function(option){
    var data = gisserverLibUtil.judgeOption(option, ['typename']);
    data.version = "1.1.0";
    data.request = "DescribeFeatureType";
    var option1 = {
        url: this.uri,
        params: data,
        async: false,
        dataType: 'text',
        success: function (data) {
            console.log("------------success----------------")
            console.log(data)
            console.log("------------success----------------")

            if (typeof option.successCallback == 'function') {
                var res = {};
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
/**
 * @des 通过要素id搜索要素
 * @option (json) 含有 typename propertyname featureid format successCallback errorCallback
 */
OGC.WFS.prototype.GetfeatureByFeatureid = function(option){
    var data = gisserverLibUtil.judgeOption(option, [ 'typename', 'propertyname', 'featureid', 'format'])
    data.version = "1.3.0";
    data.request = "GetFeature";
    var option1 = {
        url: this.uri,
        async: false,
        params: data,
        dataType: "text",
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                var res = {};
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }


    }
    gisserverLibUtil.get(option1);

}

/**
 * @des 通过范围获取要素
 * @option (json)含有 typename propertyname bbox format successCallback和errorCallback
 */
OGC.WFS.prototype.GetfeatureByBbox = function(option){
    var data = gisserverLibUtil.judgeOption(option, [ 'typename', 'propertyname', 'bbox', 'format'])
    data.version = "1.3.0";
    data.request = "GetFeature";
    var option1 = {
        url: this.uri,
        async: false,
        params: data,
        dataType: "text",
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                var res = {};
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }


    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 通过自定义筛选条件搜索要素
 * @option (json) 含有typename propertyname filter format successCallback 和 errorCallback
 */
OGC.WFS.prototype.GetfeatureByFilter = function(option){
    var data = gisserverLibUtil.judgeOption(option, [ 'typename', 'propertyname', 'filter', 'format']);
    data.version = "1.3.0";
    data.request = "GetFeature";
    var option1 = {
        url: this.uri,
        async: false,
        params: data,
        dataType: "text",
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                var res = {};
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }


    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 自定义操作
 * @option (json) 含有 req successCallback和errorCallback
 */
OGC.WFS.prototype.Transaction = function(option){
    var data = gisserverLibUtil.judgeOption(option, [ 'req']);
    var option1 = {
        url:this.uri,
        async: false,
        data: data.req,
        dataType: "text",
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                var res = {};
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }


    }
    gisserverLibUtil.postXMLStream(option1);
}

/**
 * @des 获取元数据
 * @option (json) 含有successCallback和errorCallback
 */
OGC.WMTS.prototype.GetCapabilities = function (option){
    var data = {};
    data.version = '1.3.0';
    data.request = 'GetCapabilities';
    data.service = 'WMTS';
    var option1 = {
        url: this.uri,
        params: data,
        async: false,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                var res = {};
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

OGC.WMTS.prototype.GetTile = function(option){
    var data = gisserverLibUtil.judgeOption(option, [ 'TileMatrix', 'TileRow', 'TileCol'])
    data.service = "WMTS";
    data.version = "1.0.0";
    data.request = "GetTile";
    var option1 = {
        url: this.uri,
        params: data,
        async: false,
        dataType: 'blob',
        success: function (data) {
            console.log(data)
            if (data.type == "image/jpeg" || data.type == "image/png" || data.type == "image/gif") {
                if (typeof option.successCallback == 'function') {
                    src = window.URL.createObjectURL(data);
                    var jsonObj = {};
                    jsonObj.error = 0;
                    jsonObj.data = src;
                    // alert(src)
                    option.successCallback(jsonObj);

                }
            } else {
                var reader = new FileReader();
                reader.readAsText(data, 'utf-8');
                reader.onload = function (e) {
                    console.log("-----------------blobString----------------------------------------")
                    console.log(reader.result);
                    console.log("-----------------blobString----------------------------------------")
                    if (reader.result.indexOf("ServiceException") > -1) {
                        var xmlObj = string2Xml(reader.result);
                        var str = "";
                        var xmlNodesServiceException = xmlObj.querySelectorAll("ServiceException");
                        for (var i = 0; i < xmlNodesServiceException.length; i++) {

                            str += xmlNodesServiceException[i].innerHTML;
                        }
                        var jsonObj = {};
                        console.log(data)
                        jsonObj.error = 0;
                        jsonObj.message = str;
                        option.successCallback(jsonObj)
                    } else {
                        var jsonObj = {};
                        jsonObj.error = 10000;
                        jsonObj.message = reader.result;
                        option.successCallback(jsonObj)
                    }

                }
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

OGC.D3TILE.prototype.C3dService = function(option){
    var option1 = {
        url: this.uri + '/tileset.json',
        async: false,
        dataType: 'json',
        params: {},
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

OGC.D3TILE.prototype.getTile = function(option){
    var option1 = {
        url: this.uri + '/' + option.tileName,
        async: false,
        dataType: 'text',
        params: {},
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

OGC.D3TILE.prototype.filterRoi = function(option){
    var option1 = {
        url: this.uri + '/roi/' + option.roi + "/tileset.json",
        async: false,
        dataType: 'json',
        params: {},
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

OGC.D3TILE.prototype.filterId = function(option){
    var option1 = {
        url: this.uri + '/id/'+ option.id + '/tileset.json',
        async: false,
        dataType: 'json',
        params: {},
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

