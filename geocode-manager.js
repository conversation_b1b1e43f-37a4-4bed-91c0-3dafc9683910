/*
* @author: zhang<PERSON>peng
* @date: 2021/1/25
* @des: geocode服务管理接口（gisServerManager）
* */
function GeocodeManager(url) {
    this.url = url;
}
/**
 * @des 获取服务列表
 * @successCallback  成功回调
 * @errorCallback 失败回调
 * */
GeocodeManager.prototype.list = function (successCallback, errorCallback) {
    var option1 = {
        url: this.url + '/console/geocoding/list',
        params: {},
        dataType: 'json',
        success: function (data) {
            if (!data.error) {
                data.error = 0;
                data.message = '';
            }
            if (typeof successCallback == 'function') {
                successCallback(data);
            }
        },
        error: function (data) {
            if (typeof errorCallback == 'function') {
                errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
/*
* @author: zhanglaipeng
* @date: 2021/1/4
* @des: 删除geocode服务数据库
* */
GeocodeManager.prototype.delete = function(option){
    var option1 = {
        url: this.url + '/console/geocoding/delete',
        data: {
            name: option.name,
            category: ''
        },
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}
/*
* @author: zhanglaipeng
* @date: 2021/1/4
* @des: 删除category
* */
GeocodeManager.prototype.deleteCategory = function(option){
    var option1 = {
        url: this.url + '/console/geocoding/delete',
        data: {
            name: option.name,
            category: option.category
        },
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}
/*
* @author: zhanglaipeng
* @date: 2021/1/4
* @des: 服务信息
* */
GeocodeManager.prototype.info = function (option) {
    var option1 = {
        url: this.url + '/console/geocoding/list',
        params: {},
        dataType: 'json',
        success: function (data) {
            console.log(data)
            if (data.libraries || data.libraries.length >= 0) {
                var result = {error: 0, message: ''};
                for (var i in data.libraries) {
                    if (option.name == data.libraries[i].name) {
                        if (typeof option.successCallback == 'function') {
                            result.library = data.libraries[i];
                        }
                        break;
                    }
                }
                if (!result.library) {
                    result.message = '未找到此地理编码服务！';
                    result.error = 10000;
                }
                option.successCallback(result);
            } else {
                if (typeof option.successCallback == 'function') {
                    option.successCallback({error: 500, message: '获取地理编码列表错误！'});
                }
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
/*
* @author: zhanglaipeng
* @date: 2021/1/4
* @des: 服务发布接口--excel文件
* */
GeocodeManager.prototype.publishByExcel = function (option) {
    var arr = ['category', 'excelfile', 'geoFields', 'geoType'];
    var data = gisserverLibUtil.judgeOption(option);
    data.name = option.name;
    data.action = "create";
    var option1 = {
        url: this.url + '/console/geocoding/import',
        data: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                if (typeof option.successCallback == 'function') {
                    var res = {};
                    if(data.code){
                        res.error = data.code;
                        res.message = data.message;
                    }else{
                        res.error = 0;
                        res.data = data;
                    }
                    option.successCallback(res);
                }
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.postFile(option1);
}
/*
* @author: zhanglaipeng
* @date: 2021/1/4
* @des: 服务发布接口
* */
GeocodeManager.prototype.publishByShape = function (option) {
    var arr = ['category', 'shapefile', 'dbasefile', 'charset'];
    var data = gisserverLibUtil.judgeOption(option);
    data.name = option.name;
    data.action = "create";
    var option1 = {
        url: this.url + '/console/geocoding/import',
        data: data,
        dataType: 'json',
        success: function (data) {

            if (typeof option.successCallback == 'function') {
                var res = {};
                if(data.code){
                    res.error = data.code;
                    res.message = data.message;
                }else{
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.postFile(option1);
}
/*
* @author: zhanglaipeng
* @date: 2021/1/4
* @des: 通过excel创建、追加、更新geocode服务（数据库）里的  category
* */
GeocodeManager.prototype.createAppendUpdateCategoryByExcel = function (option) {
    var arr = ['category', 'excelfile', 'geoFields', 'geoType', 'action'];
    var data = gisserverLibUtil.judgeOption(option);
    data.name = option.name;
    var option1 = {
        url: this.url + '/console/geocoding/import',
        data: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                var res = {};
                if(data.code){
                    res.error = data.code;
                    res.message = data.message;
                }else{
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.postFile(option1);
}
/*
* @author: zhanglaipeng
* @date: 2021/1/4
* @des: 通过shape创建、追加、更新 geocode服务（数据库）里的  category
* */
GeocodeManager.prototype.createAppendUpdateCategoryByShape = function (option) {
    var arr = ['category', 'shapefile', 'dbasefile', 'charset', 'action'];
    var data = gisserverLibUtil.judgeOption(option);
    data.name = option.name;
    var option1 = {
        url: this.url + '/console/geocoding/import',
        data: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                if (typeof option.successCallback == 'function') {
                    var res = {};
                    if(data.code){
                        res.error = data.code;
                        res.message = data.message;
                    }else{
                        res.error = 0;
                        res.data = data;
                    }
                    option.successCallback(res);
                }
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.postFile(option1);
}
