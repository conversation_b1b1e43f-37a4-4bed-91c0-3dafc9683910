var AgsREST = {
    MapServer: function(uri){
        this.uri = uri;
    },
    FeatureServer: function (uri){
        this.uri = uri;
    },
    GeometryServer: function(uri){
        this.uri = uri;

    },
    ImageServer: function (uri){
        this.uri = uri;
    }
}

/**
 * @des 获取rest服务元数据
 * @option (json) 含有successCallback和errorCallback
 */
AgsREST.MapServer.prototype.mapService = function(option){
    var url = this.uri;
    var res = {};
    var option1 = {
        url:  url,
        params: {},
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                if (data.error) {
                    option.successCallback(data);
                }else{
                    res.error = 0;
                    res.data = data
                    option.successCallback(res);
                }

            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
   gisserverLibUtil.get(option1);
}
/**
 * @des 导出地图服务
 * @option (json)含有 bbox bboxSR size format layers transparent imageSR f successCallback和errorCallback
 * */
AgsREST.MapServer.prototype.exportMap=function (option) {
    var arr = ['bbox', 'bboxSR', 'size', 'format', 'layers', 'transparent', 'imageSR', 'f'];
    var data = gisserverLibUtil.judgeOption(option, arr);

    var url = this.uri + '/export';
    var option1 = {
        url: url,
        params: data,
        async: false,
        dataType: 'blob',
        success: function (data) {
            // if (typeof option.successCallback == 'function') {
            //     option.successCallback(data);
            // }
            console.log('------data----------')
            console.log(data)
            if (option.f == 'image') {
                if (data.type == "image/jpeg" || data.type == "image/png" || data.type == "image/gif") {
                    if (typeof option.successCallback == 'function') {
                        src = window.URL.createObjectURL(data);
                        var jsonObj = {};
                        jsonObj.error = 0;
                        jsonObj.data = src;
                        option.successCallback(jsonObj);
                    }
                } else {
                    var reader = new FileReader();
                    reader.readAsText(data, 'utf-8');
                    reader.onload = function (e) {
                        console.log("-----------------blobString----------------------------------------")
                        console.log(reader.result);
                        console.log("-----------------blobString----------------------------------------")
                        var jsonObj = {};
                        jsonObj.error = 10000;
                        if (reader.result) {
                            jsonObj.message = reader.result;
                        } else {
                            jsonObj.message = 'Not Found'
                        }
                        option.successCallback(jsonObj)
                    }
                }
            } else {
                var reader = new FileReader();
                reader.readAsText(data, 'utf-8');
                reader.onload = function (e) {
                    console.log("-----------------blobString----------------------------------------")
                    console.log(reader.result);
                    console.log("-----------------blobString----------------------------------------")
                    var jsonObj = {};

                    if (reader.result) {
                        jsonObj.error = 0;
                        jsonObj.data = reader.result;
                    } else {
                        jsonObj.error = 10000;
                        jsonObj.message = 'Not Found'
                    }
                    option.successCallback(jsonObj)
                }
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 识别图像 获取要素信息
 * @option (json) 包含geometry geometryType layers tolerance mapExtent imageDisplay return Geometry successCallback和errorCallback
 */
AgsREST.MapServer.prototype.Identify = function(option){
    var arr = ['geometry', 'geometryType', 'layers', 'tolerance', 'mapExtent', 'imageDisplay', 'returnGeometry'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri+ '/identify';
    var option1 = {
        url: url,
        params: data,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 查找指定结果
 * @option searchText contains searchFields layers returnGeometry successCallback和errorCallback
 */
AgsREST.MapServer.prototype.Find= function(option){
    var arr = ['searchText', 'contains', 'searchFields', 'layers', 'returnGeometry'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri+ '/find';

    var option1 = {
        url: url,
        params: data,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
/**
 * @des 获取图层元数据信息
 * @option (json) 含有 layerAddress successCallback和errorCallback
 */
AgsREST.MapServer.prototype.layerMetadata = function(option){
    var url = this.uri+'/'+ option.layerAddress;
    var option1 = {
        url: url,
        data: {},
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}

/**
 * @des 对表和图层执行查询操作
 * @option (json) 含有layerAddress geometry geometryType spatialRel where objectIds outFields returnGeometry returnCountOnly outStatistics groupByFieldsForStatistics successCallback errorCallback
 */
AgsREST.MapServer.prototype.Query = function(option){
    var arr = ['geometry', 'geometryType', 'spatialRel', 'where', 'objectIds', 'outFields', 'returnGeometry', 'returnCountOnly',
        'outStatistics', 'groupByFieldsForStatistics'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri+'/' + option.layerAddress + "/query";
    var option1 = {
        url: url,
        data: data,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}

/**
 * @des 获取地图切片
 * @option (json) 含有TileMatrix TileRow TileCol successCallback和errorCallback
 */
AgsREST.MapServer.prototype.TileMap = function(option){
    var url = this.uri+'/tile/' + option.TileMatrix + '/' + option.TileRow + '/' + option.TileCol;
    var option1 = {
        url: url,
        params: {},
        dataType: 'blob',
        success: function (data) {
            // if (typeof option.successCallback == 'function') {
            //     option.successCallback(data);
            // }

            console.log('------data----------')
            console.log(data)
            if (data.type) {
                if (data.type == "image/jpeg" || data.type == "image/png" || data.type == "image/gif") {
                    if (typeof option.successCallback == 'function') {
                        src = window.URL.createObjectURL(data);
                        var jsonObj = {};
                        jsonObj.error = 0;
                        jsonObj.data = src;
                        option.successCallback(jsonObj);
                    }
                } else {
                    var reader = new FileReader();
                    reader.readAsText(data, 'utf-8');
                    reader.onload = function (e) {
                        console.log("-----------------blobString----------------------------------------")
                        console.log(reader.result);
                        console.log("-----------------blobString----------------------------------------")
                        var jsonObj = {};
                        jsonObj.error = 10000;
                        if (reader.result) {
                            jsonObj.message = reader.result;
                        } else {
                            jsonObj.message = 'Not Found'
                        }
                        option.successCallback(jsonObj)
                    }
                }
            } else {
                var jsonObj = {};
                jsonObj.error = 10000;
                jsonObj.message = data;
                option.successCallback(jsonObj);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 获取元数据信息
 * @option (json) 含有successCallback和errorCallback
 */
AgsREST.FeatureServer.prototype.FeatureService = function(option){
    var url = this.uri;
    var option1 = {
        url: url,
        params: {},
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 查询符合查询条件的要素信息
 * @option (json) 含有 layerDefs geometry geometryType spatialRel returnGeometry returnCountOnly successCallback 和 errorCallback
 */
AgsREST.FeatureServer.prototype.Query = function(option){
    var arr = ['layerDefs', 'geometry', 'geometryType', 'spatialRel', 'returnGeometry', 'returnCountOnly'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri+ '/query';
    var option1 = {
        url: url,
        data: data,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}

/**
 * @des 获取图层数据信息
 * @option (json) 含有layerAddress successCallback和errorCallback
 */
AgsREST.FeatureServer.prototype.layer = function(option){
    var url = this.uri +'/'+ option.layerAddress;
    var option1 = {
        url: url,
        data: {},
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}
/**
 * @des 查询指定图层符合查询条件要素信息
 * @option (json) 含有 layerAddress geometry geometryType spatialRel where objectIds outFields returnGeometry returnCountOnly outStatistics groupByFieldsForStatistics successCallback和 errorCallback
 */
AgsREST.FeatureServer.prototype.QueryLayer = function(option){
    var arr = ['geometry', 'geometryType', 'spatialRel', 'where', 'objectIds', 'outFields', 'returnGeometry', 'returnCountOnly',
        'outStatistics', 'groupByFieldsForStatistics'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri +'/'+ option.layerAddress + '/query';
    var option1 = {
        url: url,
        data: data,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}

/**
 * @des 查询相关记录（特征服务）
 * @option (json) 含有layerAddress objectIds relationshipId outFields definitionExpression returnGeometry successCallback 和errorCallback
 */
AgsREST.FeatureServer.prototype.QueryRelatedRecords = function(option){
    var arr = ['objectIds', 'relationshipId', 'outFields', 'definitionExpression', 'returnGeometry'];
    var data = gisserverLibUtil.judgeOption(option, arr);
        var url = this.uri +'/'+ option.layerAddress + '/queryRelatedRecords';
    var option1 = {
        url: url,
        data: data,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}

/**
 * @des 向指定图层添加要素
 * @option (json) 含有layerAddress features successCallback 和 errorCallback
 */
AgsREST.FeatureServer.prototype.AddFeature = function(option){
    var data = gisserverLibUtil.judgeOption(option, ['features']);
    var url = this.uri +'/'+ option.layerAddress + '/addFeatures';
    var option1 = {
        url: url,
        data: data,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}

/**
 * @des 更新要素
 * @option (json) 含有layerAddress features successCallback和errorCallback
 */
AgsREST.FeatureServer.prototype.UpdateFeatures = function(option){
    var data = gisserverLibUtil.judgeOption(option, ['features']);
    var url =this.uri +'/'+ option.layerAddress + '/updateFeatures';
    var option1 = {
        url: url,
        data: data,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}

/**
 * @des 删除要素
 * @option (json) 含有layerAddress objectIds where successCallback 和 errorCallback
 */
AgsREST.FeatureServer.prototype.DeleteFeatures = function(option){
    var data = gisserverLibUtil.judgeOption(option, ['objectIds', 'where']);
    var url = this.uri+'/'+ option.layerAddress + '/deleteFeatures';

    var option1 = {
        url: url,
        data: data,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}

/**
 * @des 图层要素增删改操作的应用
 * @option (json) 含有adds updates deletes successCallback和errorCallback
 */
AgsREST.FeatureServer.prototype.applyEdits = function(option){
    var arr = ['adds', 'updates', 'deletes'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri +'/'+ option.layerAddress + '/applyEdits';

    var option1 = {
        url: url,
        data: data,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}

/**
 * @des 获取元数据信息
 * @option (json) 含有successCallback和errorCallback
 */
AgsREST.ImageServer.prototype.mapService = function(option){
    var url = this.uri;
    var option1 = {
        url: url,
        params: {},
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1)
}

/**
 * @des 获取地图切片
 * @option (json) 含有TileMatrix TileRow TileCol successCallback和errorCallback
 */
AgsREST.ImageServer.prototype.TileMap = function(option){
    var url = this.uri + '/tile/' + option.TileMatrix + '/' + option.TileRow + '/' + option.TileCol;
    var option1 = {
        url: url,
        params: {},
        dataType: 'blob',
        success: function (data) {
            // if (typeof option.successCallback == 'function') {
            //     option.successCallback(data);
            // }

            console.log('------data----------')
            console.log(data)
            if (data.type) {
                if (data.type == "image/jpeg" || data.type == "image/png" || data.type == "image/gif") {
                    if (typeof option.successCallback == 'function') {
                        src = window.URL.createObjectURL(data);
                        var jsonObj = {};
                        jsonObj.error = 0;
                        jsonObj.data = src;
                        option.successCallback(jsonObj);
                    }
                } else {
                    var reader = new FileReader();
                    reader.readAsText(data, 'utf-8');
                    reader.onload = function (e) {
                        console.log("-----------------blobString----------------------------------------")
                        console.log(reader.result);
                        console.log("-----------------blobString----------------------------------------")
                        var jsonObj = {};
                        jsonObj.error = 10000;
                        if (reader.result) {
                            jsonObj.message = reader.result;
                        } else {
                            jsonObj.message = 'Not Found'
                        }
                        option.successCallback(jsonObj)
                    }
                }
            } else {
                var jsonObj = {};
                jsonObj.error = 10000;
                jsonObj.message = data;
                option.successCallback(jsonObj);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 几何服务
 * @option (json) 含有successCallback和errorCallback
 */
AgsREST.GeometryServer.prototype.geometryService = function(option){
    var url = this.uri;
    var option1 = {
        url: url,
        params: {},
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 计算面积和长度
 * @option (json) 含有polygons sr lengthUnit areaUnit unionResults successCallback 和errorCallback
 */
AgsREST.GeometryServer.prototype.areasAndLengths = function(option){
    var arr = ['polygons', 'sr', 'lengthUnit', 'areaUnit', 'unionResults'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/areasAndLengths';
    var option1 = {
        url: url,
        params: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 缓冲分析
 * @option (json) 含有 geometries inSR outSR bufferSR distances unit unionResults successCallback 和 errorCallback
 */
AgsREST.GeometryServer.prototype.buffer = function(option){
    var arr = ['geometries', 'inSR', 'outSR', 'bufferSR', 'distances', 'unit', 'unionResults'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/buffer';
    var option1 = {
        url: url,
        params: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 获取凸包算法
 * @option (json) 含有geometries sr successCallback和errorCallback
 */
AgsREST.GeometryServer.prototype.convexHull = function(option){
    var arr = ['geometries', 'sr'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/convexHull';
    var option1 = {
        url: url,
        params: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 不相交部分
 * @option (json) 含有geometries geometry sr successCallback 和 errorCallback
 */
AgsREST.GeometryServer.prototype.difference = function(option){
    var arr = ['geometries', 'geometry', 'sr'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/difference';
    var option1 = {
        url: url,
        params: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 两点间距离
 * @option (json) 含有 geometry1 geometry2 sr distanceUnit geodisic successCallback 和 errorCallback
 */
AgsREST.GeometryServer.prototype.distance = function(option){
    var arr = ['geometry1', 'geometry2', 'sr', 'distanceUnit', 'geodesic'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri+ '/distance';
    var option1 = {
        url: url,
        params: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 相交部分
 * @option (json) 含有 geometries geometry sr successCallback 和 errorCallback
 */
AgsREST.GeometryServer.prototype.intersect = function (option){
    var arr = ['geometries', 'geometry', 'sr'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/intersect';
    var option1 = {
        url: url,
        params: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 获取文本位置
 * @option (json) 含有 polygons sr successCallback 和 errorCallback
 */
AgsREST.GeometryServer.prototype.labelPoints = function(option){
    var arr = ['polygons', 'sr'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/labelPoints';
    var option1 = {
        url: url,
        params: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 获取线段长度
 * @option (json) 含有 polylines sr lengthUnit geodesic successCallback 和 errorCallback
 */
AgsREST.GeometryServer.prototype.lengths = function(option){
    var arr = ['polylines', 'sr', 'lengthUnit', 'geodesic'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/lengths';
    var option1 = {
        url: url,
        params: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 坐标系转换
 * @option (json) 含有geometries inSR outSR successCallback 和 errorCallback
 */
AgsREST.GeometryServer.prototype.project = function(option){
    var arr = ['geometries', 'inSR', 'outSR'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/project';
    var option1 = {
        url: url,
        params: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 判断几何关系
 * @option (json) 含有geometries1 geometries2 sr relation successCallback 和 errorCallback
 */
AgsREST.GeometryServer.prototype.relation = function(option){
    var arr = ['geometries1', 'geometries2', 'sr', 'relation'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/relation';
    var option1 = {
        url: url,
        params: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 求外接框
 * @option (json) 含有geometries sr successCallback 和 errorCallback
 */
AgsREST.GeometryServer.prototype.simplify = function(option){
    var arr = ['geometries', 'sr'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/simplify';
    var option1 = {
        url: url,
        params: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 合并几何
 * @option (json) 含有geometries sr successCallback 和errorCallback
 */
AgsREST.GeometryServer.prototype.union = function(option){
    var arr = ['geometries', 'sr'];
    var data = gisserverLibUtil.judgeOption(option, arr);
    var url = this.uri + '/union';
    var option1 = {
        url: url,
        params: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}



