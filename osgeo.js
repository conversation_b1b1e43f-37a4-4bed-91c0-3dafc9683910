var OSGEO = {
    TMS: function (uri){
        this.uri = uri;
    }
}

/**
 * @des 获取元数据服务
 * @option (json) 含有successCallback和errorCallback
 */
OSGEO.TMS.prototype.TileMap = function(option){
    var data = {};
    data.version = '1.3.0';
    data.request = 'GetCapabilities';
    data.service = 'TMS';
    var option1 = {
        url: this.uri,
        params: data,
        async: false,
        dataType: 'text',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                var res = {};
                if (data.error) {
                    res.error = data.error;
                    res.message = data.message;
                } else {
                    res.error = 0;
                    res.data = data;
                }
                option.successCallback(res);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

/**
 * @des 获取切片服务
 * @option (json) 含有level x y successCallback和errorCallback
 */
OSGEO.TMS.prototype.Tile = function(option){
    var arr = [ 'level', 'x', 'y']
    var data = gisserverLibUtil.judgeOption(option, arr);
    // http://localhost:8080/gisserver/tmsserver/china_tms/3/6/4.png
    var option1 = {
        url: this.uri + '/' + data.level + '/' + data.x + '/' + data.y + '.png',
        async: false,
        dataType: 'blob',
        success: function (data) {
            console.log(data)
            if (data.type == "image/jpeg" || data.type == "image/png" || data.type == "image/gif") {
                if (typeof option.successCallback == 'function') {
                    src = window.URL.createObjectURL(data);
                    var jsonObj = {};
                    jsonObj.error = 0;
                    jsonObj.data = src;
                    // alert(src)
                    option.successCallback(jsonObj);

                }
            } else {
                var reader = new FileReader();
                reader.readAsText(data, 'utf-8');
                reader.onload = function (e) {
                    console.log("-----------------blobString----------------------------------------")
                    console.log(reader.result);
                    console.log("-----------------blobString----------------------------------------")
                    if (reader.result.indexOf("ServiceException") > -1) {
                        var xmlObj = string2Xml(reader.result);
                        var str = "";
                        var xmlNodesServiceException = xmlObj.querySelectorAll("ServiceException");
                        for (var i = 0; i < xmlNodesServiceException.length; i++) {

                            str += xmlNodesServiceException[i].innerHTML;
                        }
                        var jsonObj = {};
                        console.log(data)
                        jsonObj.error = 0;
                        jsonObj.message = str;
                        option.successCallback(jsonObj)
                    } else {
                        var jsonObj = {};
                        jsonObj.error = 10000;
                        jsonObj.message = reader.result;
                        option.successCallback(jsonObj)
                    }

                }
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}

