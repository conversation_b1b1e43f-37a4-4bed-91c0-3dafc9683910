var licenseManager = {
    url: '',
    setServerUrl: function(url){
        this.url = url;
    },
    getInfo: function(successCallback, errorCallback){
        var option = {
            url: this.url + '/api/licence',
            params: {},
            dataType: 'json',
            success: function (data){
                if(typeof successCallback == 'function'){
                    successCallback(data);
                }
            },
            error: function (){
                if(typeof errorCallback == 'function'){
                    errorCallback(data);
                }
            }
        }
        gisserverLibUtil.get(option);
    }
}
