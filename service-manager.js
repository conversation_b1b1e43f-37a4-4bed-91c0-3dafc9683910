/*
* @author: z<PERSON><PERSON><PERSON>g
* @date: 2021/12/29
* @des: 服务管理接口（gisServerManager）
* */
function ServiceManager(url) {
    this.url = url;
}

/**
 * @des 获取服务列表
 * @successCallback  成功回调
 * @errorCallback 失败回调
 * */
ServiceManager.prototype.list = function (option) {
    var data = {
            method: 'list',
        f: 'json'
    };
    if (option.type)
        data.type = option.type;
    if (option.group)
        data.group = option.group;
    var option1 = {
        url: this.url + '/console',
        data: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}
/*
* @des 启动服务
* @name 服务名称
* @type 服务类型
* @callback 回调函数
* */
ServiceManager.prototype.startService = function (name, successCallback, errorCallback) {
    var option1 = {
        url: this.url + '/console',
        params: {
            name: name,
            method: 'start',
            f: 'json'
        },
        dataType: 'json',
        success: function (data) {
            if (typeof successCallback == 'function') {
                successCallback(data);
            }
        },
        error: function (data) {
            if (typeof errorCallback == 'function') {
                errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
/*
* @des 停止服务
* @name 服务名称
* @type 服务类型
* @callback 回调函数
* */
ServiceManager.prototype.stopService = function (name, successCallback, errorCallback) {
    var option1 = {
        url: this.url + '/console',
        params: {
            name: name,
            method: 'stop',
            f: 'json'
        },
        dataType: 'json',
        success: function (data) {
            if (typeof successCallback == 'function') {
                successCallback(data);
            }
        },
        error: function (data) {
            if (typeof errorCallback == 'function') {
                errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
/*
* @des 删除服务
* @name 服务名称
* @type 服务类型
* @callback 回调函数
* */
ServiceManager.prototype.deleteService = function (name, successCallback, errorCallback) {
    var option1 = {
        url: this.url + '/console',
        params: {
            name: name,
            method: 'remove',
            f: 'json'
        },
        dataType: 'json',
        success: function (data) {
            if (typeof successCallback == 'function') {
                successCallback(data);
            }
        },
        error: function (data) {
            if (typeof errorCallback == 'function') {
                errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);
}
/*
* @des 服务详情
* @name 服务名称
* @type 服务类型
* @callback 回调函数
* */
ServiceManager.prototype.serviceInfo = function (name, successCallback, errorCallback) {
    var option1 = {
        url: this.url + '/console',
        params: {
            name: name,
            method: 'props',
            f: 'json'
        },
        dataType: 'json',
        success: function (data) {
            if (typeof successCallback == 'function') {
                successCallback(data);
            }
        },
        error: function (data) {
            if (typeof errorCallback == 'function') {
                errorCallback(data);
            }
        }
    }
    gisserverLibUtil.get(option1);

}
ServiceManager.prototype.publishService = function (name, type, option) {
    var data = gisserverLibUtil.judgeOption(option, []);
    data.type = type;
    data.name = name;
    data.method = 'create';
    data.f = 'json';
    var that = this;
    var option1 = {
        url: this.url + '/console',
        data: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                that.startService(name, function(data1){
                    option.successCallback(data);
                }, function(e){
                    option.successCallback(data);
                })
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.postFile(option1);
}
/**
 * @des wms  wfs  wns tms 统一发布接口
 * @param option 传入参数
 * name 服务名 tile 标题  group 分组   type 服务类型 file上传文件（tms用这个参数）
 *  path 上传文件（wms、wfs、wns用这个参数）  andWFS(发布wms服务时，可加此参数发布wfs,不发布不要此参数)
 *  andWNS(发布wms或者wfs时，可加此参数发布wns服务，不发布不要此参数)
 */
ServiceManager.prototype.publishServiceByEdp = function (name, type, option) {
    if (option.type == "TMS") {
        var data = gisserverLibUtil.judgeOption(option, ['title', 'group', 'file']);
    } else {
        var data = gisserverLibUtil.judgeOption(option, ['title', 'group', 'path', 'andWFS', 'andWNS']);

    }
    data.method = 'create';
    data.f = 'json';
    data.name = name;
    data.type = type;
    const that = this;
    var option1 = {
        url: this.url + '/console',
        data: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                var serviceNum = 1;
                var startServiceCallBackNum = 0;
                that.startService(name, function(data1){
                    startServiceCallBackNum++;
                    startServiceCallBack()
                }, function(e){
                    startServiceCallBackNum++;
                    startServiceCallBack()
                })
                if(option["andWFS"]){
                    serviceNum++;
                    that.startService(name+"_wfs", function(data1){
                        startServiceCallBackNum++;
                        startServiceCallBack()
                    }, function(e){
                        startServiceCallBackNum++;
                        startServiceCallBack()
                    })
                }
                if(option["andWNS"]){
                    serviceNum++;
                    that.startService(name+"_wns", function(data1){
                        startServiceCallBackNum++;
                        startServiceCallBack()
                    }, function(e){
                        startServiceCallBackNum++;
                        startServiceCallBack()
                    })
                }
                function startServiceCallBack(){
                    if(serviceNum == startServiceCallBackNum){
                        option.successCallback(data);
                    }
                }
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.postFile(option1);
}

ServiceManager.prototype.edit = function (name, type, option) {
    var data = gisserverLibUtil.judgeOption(option, []);
    data.method = "edit";
    data.name = name;
    data.type = type;
    data.f = 'json';
    var option1 = {
        url: this.url + '/console',
        data: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.post(option1);
}

ServiceManager.prototype.Spupdate = function (name, type, option) {
    var data = gisserverLibUtil.judgeOption(option, []);
    data.method = "modify";
    data.name = name;
    data.type = type;
    data.f = 'json';
    data.action = 'updateModels';

    var option1 = {
        url: this.url + '/console',
        data: data,
        dataType: 'json',
        success: function (data) {
            if (typeof option.successCallback == 'function') {
                option.successCallback(data);
            }
        },
        error: function (data) {
            if (typeof option.errorCallback == 'function') {
                option.errorCallback(data);
            }
        }
    }
    gisserverLibUtil.postFile(option1);
}
