/*
* @author: <PERSON><PERSON><PERSON><PERSON><PERSON>、yuy<PERSON><PERSON>n、haochongchong
* @date: 2021/12/28
* @des: gisServerManager接口
* */
function GisServer(url) {
    //变量赋值
    this.url = url;
    //接口实例化
    this.serviceManager = new ServiceManager(url);
    this.geocodeManager = new GeocodeManager(url);
    //版本赋值
    this.Version = '1.0.0';
}
GisServer.prototype.setLoginPage = function(url){
    GisServer.prototype.loginUrl = url;
}
GisServer.prototype.jumpLogin = function(data, callback){
    if(data.error === 401 && GisServer.prototype.loginUrl){
        localStorage.removeItem("gisserverLoginCode");
        localStorage.setItem('gisserverLoginCode', 401);
        localStorage.removeItem("gisserverLoginCodeTime");
        localStorage.setItem('gisserverLoginCodeTime', (new Date()).Format('yyyy-MM-dd hh:mm:ss'));
        window.location.href = GisServer.prototype.loginUrl;//+ "?type=Unauthorized";
    } else {
        if(typeof callback == "function"){
            callback(data);
        }
    }
}
/***
 *
 * 可取值'OGC:WMS'、'OGC:WFS'、'OGC:WMTS'、'OGC:3DTILE'、'OSGEO:TMS'、'AgsREST:MapServer'、
 * 'AgsREST:FeatureServer'、'AgsREST:GeometryServer'、'AgsREST:ImageServer'、
 * 'SE:Geocode'、'SE:Data'、'SE:CTS'、'SE:WNS'、'SE:WCS'
 * 返回值：接口对象
 * */
GisServer.prototype.getService = function(name, interfaceType){
    var uri = this.url;
    var result = null;
    switch (interfaceType) {
        case 'OGC:WMS':
            uri = uri + '/wmsserver/' + name;
            result = new OGC.WMS(uri);
            break;
        case 'OGC:WFS':
            uri = uri + '/wfsserver/' + name;
            result = new OGC.WFS(uri);
            break;
        case 'OGC:WMTS':
            uri = uri + '/tmsserver/' + name;
            result = new OGC.WMTS(uri);
            break;
        case 'OGC:3DTILE':
            uri = uri + '/c3dserver/' + name;
            result = new OGC.D3TILE(uri);
            break;
        case 'OSGEO:TMS':
            uri = uri + '/tmsserver/' + name;
            result = new OSGEO.TMS(uri);
            break;
        case 'AgsREST:MapServer':
            uri = uri + '/rest/services/' + name + '/MapServer';
            result = new AgsREST.MapServer(uri);
            break;
        case 'AgsREST:FeatureServer':
            uri = uri + '/rest/services/' + name + '/FeatureServer';
            result = new AgsREST.FeatureServer(uri);
            break;
        case 'AgsREST:GeometryServer':
            uri = uri + '/rest/services/' + name + '/GeometryServer';
            result = new AgsREST.GeometryServer(uri);
            break;
        case 'AgsREST:ImageServer':
            uri = uri + '/rest/services/' + name + '/ImageServer';
            result = new AgsREST.ImageServer(uri);
            break;
        case 'SE:Geocode':
            uri = uri + '/rest/services/Locators/' + name + '/GeocodeServer';
            result = new SE.Geocode(uri);
            break;
        case 'SE:Data':
            uri = uri + '/dataserver/import';
            result = new SE.Data(uri);
            break;
        case 'SE:CTS':
            uri = uri + '/ctsserver/'+name;
            result = new SE.CTS(uri);
            break;
        case 'SE:WNS':
            uri = uri + '/wnsserver/' + name;
            result = new SE.WNS(uri);
            break;
        case 'SE:WCS':
            // uri = uri + '/rest/services/' + name + '/ImageServer';
            result = new SE.WCS(uri, name);
            break;
    }
    return result;
}
/*
* @des 登录接口
* @param username 用户名
* @param password 密码
* @param callback 回调函数
* */
GisServer.prototype.login = function (username, password, callback){
    if (!username) {
        alert('请传入用户名！');
        return false;
    }
    if (!password) {
        alert('请传入密码！');
        return false;
    }
    var data1 = {
        username: username,
        password: password,
        // plan: false,
        f: 'json'
    }
    var option = {
        url: this.url + '/login',
        data: data1,
        dataType: 'json',
        success: function (data){
            if(typeof callback == 'function'){
                callback(data);
            }
        },
        error: function (){
            if(typeof callback == 'function'){
                callback(data);
            }
        }
    }
    gisserverLibUtil.post(option);
}
/*
* @des 修改密码接口
* @param option 传入参数
*   oldPwd 原密码
*   newPwd 新密码
*   confirmPwd 确认密码
* */
GisServer.prototype.changePassWord = function (option){
    var option1 = {
        url: this.url + '/chgpass',
        data: {
            old: option.oldPwd,
            new1: option.newPwd,
            new2: option.confirmPwd,
        },
        dataType: 'text',
        success: function(data){
            if(typeof data == 'object'){
                if(typeof option.callback == 'function'){
                    option.callback(data);
                }
            } else if(typeof data == 'string'){
                var result = {error: 0, message: ''};
                if (data.indexOf('修改密码成功!') > -1) {
                    result.message = '修改密码成功!';
                } else if (data.indexOf('旧密码不正确') > -1) {
					result.error = 500;
                    result.message = '旧密码不正确!';
                } else {
                    result.error = 500;
                    result.message = '修改密码失败!';
                }
                if(typeof option.callback == 'function'){
                    option.callback(result);
                }
            } else {
                option.callback({error:500, message: data});
            }
        },
        error: function (e){
            if(typeof option.callback == 'function'){
                option.callback(e);
            }
        }
    }
    gisserverLibUtil.post(option1);
}
/*
* @des 登出接口
* @param callback 回调函数
* */
GisServer.prototype.logout = function (callback){
    var option1 = {
        url: this.url + '/console',
        params: {
            logout: 1
        },
        dataType: 'text',
        success: function(data){
            if(typeof callback == 'function'){
                if(data.error == 401){
                    callback({error:0,message:'退出成功！'});
                } else {
                    callback(data);
                }
            }
        },
        error: function(e){
            if(typeof callback == 'function'){
                callback({error:0,message:'退出成功'});
            }
        }
    }
    gisserverLibUtil.get(option1);
}
GisServer.prototype.getKey = function (callback){
	var option = {
		url: this.url + '/login',
		params: {},
		dataType: 'json',
		success: function(data){
		    if(typeof callback == 'function'){
		        if(data.error == 401){
		            callback({error:1,message:'获取秘钥失败!'});
		        } else {
		            callback(data);
		        }
		    }
		},
		error: function(e){
		    if(typeof callback == 'function'){
		        callback({error:1,message:'获取秘钥失败!'});
		    }
		}
	}
	gisserverLibUtil.get(option);
}
GisServer.prototype.isLogin = function(callback){
    this.serviceManager.list({
        group: '1',
        type: 'wfs',
        successCallback: function(data) {
            if(typeof jumpToLogin == 'function'){
                var result = jumpToLogin(data);
                if (result) return true;
            }
            if(typeof callback == 'function'){
                callback(true);
            }
        },
        errorCallback: function(e) {
            console.log("获取serviceList.json失败，原因", e);
            if(typeof callback == 'function'){
                callback(false);
            }
        }
    });
}
GisServer.prototype.getVersion = function (callback){
    var option = {
        url: this.url + '/version.txt',
        params: {},
        dataType: 'text',
        success: function(data){
            if(typeof callback == 'function'){
                if(data.error == 401){
                    callback({error:1,message:'获取版本号失败!'});
                } else {
                    callback(data);
                }
            }
        },
        error: function(e){
            if(typeof callback == 'function'){
                callback({error:1,message:'获取版本号失败!'});
            }
        }
    }
    gisserverLibUtil.get(option);
}










