function initGisServer(){
    var path = './lib/gisserver-lib/';
    addScriptByWrite(path + 'gisserver-public-method.js');
    addScriptByWrite(path + 'gisserver-lib-util.js');
    addScriptByWrite(path + 'ogc.js');
    addScriptByWrite(path + 'osgeo.js');
    addScriptByWrite(path + 'ags-rest.js');
    addScriptByWrite(path + 'se.js');
    addScriptByWrite(path + 'service-manager.js');
    addScriptByWrite(path + 'geocode-manager.js');
    addScriptByWrite(path + 'gisserver.js');

    addScriptByWrite(path + 'daily-record.js');
    addScriptByWrite(path + 'license-manager.js');
}
function addScriptByWrite(url){
    document.write('<script src="'+url+'"></script>')
}
initGisServer();
