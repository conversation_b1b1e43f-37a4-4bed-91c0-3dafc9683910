function GisServerContent (obj){
    function F(){}
    F.prototype = obj;
    return new F();
}
/**
 * 字符串转xml对象
 * @param xmlString
 * @returns {*}
 * @constructor
 */
function string2Xml(xmlString) {
    if (window.ActiveXObject !== undefined) { // for IE
        var xmlObject = new ActiveXObject("Microsoft.XMLDOM");
        xmlObject.async = "false";
        xmlObject.loadXML(xmlString);
        return xmlObject;
    }
    else { // for other browsers
        var parser = new DOMParser();
        var xmlObject = parser.parseFromString(xmlString, "text/xml");
        return xmlObject;
    }
}
/**
 * 将xml对象转json对象
 * @param xml
 */
function Xml2Json(xml) {
    var js_obj = {};
    if (xml.nodeType === Node.ELEMENT_NODE) { // 元素节点
        var _attr = xml.attributes;
        if (_attr.length > 0) {
            for (var j = 0; j < _attr.length; j++) {
                // 属性前面用‘@’标注，可以自定义
                js_obj['@' + _attr[j].nodeName] = _attr[j].value;
            }
        }
    } else if (xml.nodeType === Node.TEXT_NODE) { // 文本
        js_obj = (xml.nodeValue.replace(/[\ +\r\n]/g, "") === "") ? "" : xml.nodeValue;
    }
    if (xml.childNodes) {
        var nodes = xml.childNodes;
        for (var i = 0; i < nodes.length; i++) {
            var nodeName = nodes[i].nodeName;
            if (typeof(js_obj[nodeName]) === "undefined") {
                var _jsObj = Xml2Json(nodes[i]); // 递归
                if (_jsObj !== "") {
                    js_obj[nodeName] = _jsObj;
                }
            } else {
                if (typeof(js_obj[nodeName].push) === "undefined") {
                    var old = js_obj[nodeName];
                    js_obj[nodeName] = [];
                    js_obj[nodeName].push(old);
                }
                var _jsObj = Xml2Json(nodes[i]); // 递归
                if (_jsObj !== "") {
                    js_obj[nodeName].push(_jsObj);
                }
            }
        }
    }
    return js_obj;
}
//格式化xml代码
/**
 * @des 格式化xml
 * @xmlStr xml字符串
 * @returns 格式化后的xml字符串
 */
function formatXml(xmlStr){
    text = xmlStr;
    //使用replace去空格
    text = '\n' + text.replace(/(<\w+)(\s.*?>)/g,function($0, name, props){
        return name + ' ' + props.replace(/\s+(\w+=)/g," $1");
    }).replace(/>\s*?</g,">\n<");
    //处理注释
    text = text.replace(/\n/g,'\r').replace(/<!--(.+?)-->/g,function($0, text){
        var ret = '<!--' + escape(text) + '-->';
        return ret;
    }).replace(/\r/g,'\n');
    //调整格式  以压栈方式递归调整缩进
    var rgx = /\n(<(([^\?]).+?)(?:\s|\s*?>|\s*?(\/)>)(?:.*?(?:(?:(\/)>)|(?:<(\/)\2>)))?)/mg;
    var nodeStack = [];
    var output = text.replace(rgx,function($0,all,name,isBegin,isCloseFull1,isCloseFull2 ,isFull1,isFull2){
        var isClosed = (isCloseFull1 == '/') || (isCloseFull2 == '/' ) || (isFull1 == '/') || (isFull2 == '/');
        var prefix = '';
        if(isBegin == '!'){//!开头
            prefix = setPrefix(nodeStack.length);
        }else {
            if(isBegin != '/'){///开头
                prefix = setPrefix(nodeStack.length);
                if(!isClosed){//非关闭标签
                    nodeStack.push(name);
                }
            }else{
                nodeStack.pop();//弹栈
                prefix = setPrefix(nodeStack.length);
            }
        }
        var ret =  '\n' + prefix + all;
        return ret;
    });
    var prefixSpace = -1;
    var outputText = output.substring(1);
    //还原注释内容
    outputText = outputText.replace(/\n/g,'\r').replace(/(\s*)<!--(.+?)-->/g,function($0, prefix,  text){
        if(prefix.charAt(0) == '\r')
            prefix = prefix.substring(1);
        text = unescape(text).replace(/\r/g,'\n');
        var ret = '\n' + prefix + '<!--' + text.replace(/^\s*/mg, prefix ) + '-->';
        return ret;
    });
    outputText= outputText.replace(/\s+$/g,'').replace(/\r/g,'\r\n');
    return outputText;
}

//计算头函数 用来缩进
function setPrefix(prefixIndex) {
    var result = '';
    var span = '    ';//缩进长度
    var output = [];
    for(var i = 0 ; i < prefixIndex; ++i){
        output.push(span);
    }
    result = output.join('');
    return result;
}
//下载文件
function public_download_file(url, fileName){
    console.log("downloadfile",url, fileName);
    const aDom = document.createElement('a');
    aDom.href = url;
    aDom.download = fileName;
    document.body.appendChild(aDom);
    aDom.click();
    document.body.removeChild(aDom);
}